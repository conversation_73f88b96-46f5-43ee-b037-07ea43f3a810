# 算法管理接口文档

## 接口总览

本文档描述了算法管理系统的REST API接口，提供算法信息的查询、搜索和状态管理功能。

**基础路径**: `/algorithm`

---

## 1. 搜索算法接口

### 功能描述
根据算法类型或算法名称进行分页搜索，支持模糊查询。

### 接口信息
- **端点路径**: `/algorithm/search`
- **HTTP方法**: `GET`
- **权限要求**: 需要登录

### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| name | String | 否 | - | 算法名称关键字，支持模糊查询 |
| type | String | 否 | - | 算法类型，可选值见下表 |
| pageNum | Integer | 否 | 1 | 页码，必须大于0 |
| pageSize | Integer | 否 | 10 | 每页条数，范围1-100 |

#### 算法类型说明
| 类型值 | 说明 |
|--------|------|
| UNSUPERVISED | 无监督算法 |
| SUPERVISED | 监督算法 |
| DEEP_LEARNING | 深度学习算法 |

### 请求示例
```http
GET /algorithm/search?name=聚类&type=UNSUPERVISED&pageNum=1&pageSize=10
```

### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 15,
    "list": [
      {
        "id": 1,
        "name": "K-means聚类算法",
        "direction": "数据聚类分析",
        "description": "基于距离的聚类算法，适用于球状聚类",
        "type": 1,
        "typeName": "无监督算法",
        "enabled": true,
        "enabledText": "启用"
      },
      {
        "id": 2,
        "name": "DBSCAN聚类算法",
        "direction": "密度聚类",
        "description": "基于密度的聚类算法，能发现任意形状的聚类",
        "type": 1,
        "typeName": "无监督算法",
        "enabled": false,
        "enabledText": "禁用"
      }
    ]
  }
}
```

**错误响应 (400)**:
```json
{
  "code": 400,
  "message": "每页条数必须在1-100之间",
  "data": null
}
```

---

## 2. 获取算法详情接口

### 功能描述
根据算法ID获取算法的详细信息。

### 接口信息
- **端点路径**: `/algorithm/{id}`
- **HTTP方法**: `GET`
- **权限要求**: 需要登录

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 算法ID |

### 请求示例
```http
GET /algorithm/1
```

### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "K-means聚类算法",
    "direction": "数据聚类分析",
    "description": "基于距离的聚类算法，适用于球状聚类。通过迭代优化聚类中心，将数据点分配到最近的聚类中心。",
    "type": 1,
    "typeName": "无监督算法",
    "enabled": true,
    "enabledText": "启用"
  }
}
```

**错误响应 (404)**:
```json
{
  "code": 404,
  "message": "算法不存在",
  "data": null
}
```

**错误响应 (500)**:
```json
{
  "code": 500,
  "message": "获取算法详情失败：数据库连接异常",
  "data": null
}
```

---

## 3. 修改算法状态接口

### 功能描述
修改算法的启用/禁用状态。注意：只能更新启停状态，不能修改其他信息。

### 接口信息
- **端点路径**: `/algorithm/update`
- **HTTP方法**: `PUT`
- **权限要求**: 需要登录

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 算法ID，必须大于0 |
| enabled | Boolean | 是 | 启用状态，true-启用，false-禁用 |

### 请求示例
```http
PUT /algorithm/update?id=1&enabled=false
```

### 响应示例

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

**错误响应 (400)**:
```json
{
  "code": 400,
  "message": "算法ID不能为空且必须大于0",
  "data": null
}
```

**错误响应 (500)**:
```json
{
  "code": 500,
  "message": "更新算法状态失败，可能算法不存在",
  "data": null
}
```

---

## 数据模型

### AlgorithmDto (算法信息)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 算法主键ID |
| name | String | 算法名称 |
| direction | String | 算法擅长方向 |
| description | String | 算法详细介绍 |
| type | Integer | 算法类型编码（1-无监督算法，2-监督算法，3-深度学习算法） |
| typeName | String | 算法类型名称 |
| enabled | Boolean | 启用状态 |
| enabledText | String | 启用状态文本描述 |

### PageResult (分页结果)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | Long | 总记录数 |
| list | Array | 数据列表 |

### Result (统一响应格式)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码（200-成功，400-参数错误，404-资源不存在，500-服务器错误） |
| message | String | 响应消息 |
| data | Object | 响应数据，成功时包含具体数据，失败时为null |

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 注意事项

1. 所有接口都需要在请求头中包含有效的认证信息
2. 分页查询的页码从1开始，每页最大条数限制为100
3. 算法状态修改接口只能更新启停状态，不支持修改其他字段
4. 所有时间相关字段采用ISO 8601格式
5. 响应数据统一采用UTF-8编码 