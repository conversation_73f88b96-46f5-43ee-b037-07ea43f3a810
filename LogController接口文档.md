# LogController 接口文档

## 概述
操作日志控制器，负责记录和查询系统操作日志。

## 接口列表

### 1. 记录操作日志

**功能描述**：记录用户在平台的操作日志

**端点路径**：`POST /log`

**HTTP方法**：POST

**请求头**：
```
Content-Type: application/json
```

**请求体**：
```json
{
  "requestTime": "2024-01-15T10:30:00",
  "operator": "张三",
  "title": "查询卫星数据",
  "requestUrl": "/satellite/search",
  "requestMethod": "GET",
  "ipAddress": "*************",
  "client": "Web",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "durationMs": 150,
  "requestParam": "{\"name\":\"卫星1\",\"status\":\"active\"}"
}
```

**请求参数说明**：
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| requestTime | string | 是 | 请求时间，格式：yyyy-MM-ddTHH:mm:ss |
| operator | string | 是 | 操作人姓名 |
| title | string | 是 | 操作标题 |
| requestUrl | string | 是 | 请求地址 |
| requestMethod | string | 是 | 请求方式（GET/POST等） |
| ipAddress | string | 是 | IP地址 |
| client | string | 是 | 客户端类型 |
| userAgent | string | 否 | 浏览器用户代理 |
| durationMs | integer | 是 | 耗时（毫秒） |
| requestParam | string | 否 | 请求参数JSON字符串 |

**响应示例**：

成功响应：
```json
{
  "code": 200,
  "message": "操作日志记录成功",
  "data": null
}
```

失败响应：
```json
{
  "code": 400,
  "message": "记录操作日志失败：参数验证失败",
  "data": null
}
```

---

### 2. 搜索操作日志

**功能描述**：搜索操作日志，支持根据操作人姓名搜索和时间区间筛选

**端点路径**：`GET /log/search`

**HTTP方法**：GET

**请求参数**：
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| userName | string | 否 | - | 操作人姓名（模糊匹配） |
| startTime | string | 否 | - | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime | string | 否 | - | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| pageNo | integer | 否 | 1 | 页码 |
| pageSize | integer | 否 | 10 | 每页条数 |

**请求示例**：
```
GET /log/search?userName=张三&startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59&pageNo=1&pageSize=10
```

**响应示例**：

成功响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "list": [
      {
        "id": 1,
        "requestTime": "2024-01-15T10:30:00",
        "operator": "张三",
        "title": "查询卫星数据",
        "requestUrl": "/satellite/search",
        "requestMethod": "GET",
        "ipAddress": "*************",
        "client": "Web",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "durationMs": 150,
        "requestParam": "{\"name\":\"卫星1\",\"status\":\"active\"}",
        "createTime": "2024-01-15T10:30:01"
      },
      {
        "id": 2,
        "requestTime": "2024-01-15T11:00:00",
        "operator": "张三",
        "title": "更新算法配置",
        "requestUrl": "/algorithm/update",
        "requestMethod": "PUT",
        "ipAddress": "*************",
        "client": "Web",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "durationMs": 300,
        "requestParam": "{\"id\":1,\"name\":\"新算法\"}",
        "createTime": "2024-01-15T11:00:01"
      }
    ]
  }
}
```

失败响应：
```json
{
  "code": 500,
  "message": "搜索操作日志失败：数据库连接异常",
  "data": null
}
```

## 响应状态码说明

| 状态码 | 描述 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有时间参数需要严格按照指定格式传递
2. 记录操作日志时，必填字段不能为空
3. 搜索操作日志支持分页，建议设置合理的每页条数
4. 时间筛选支持精确到秒的查询
5. 操作人姓名支持模糊匹配查询 