# MQTT连接管理接口文档

## 接口总结

MQTT连接管理接口用于航天器系统中的MQTT消息队列连接配置与管理，支持创建、测试、更新、删除和查询MQTT连接配置。该模块主要用于航天器遥测数据的实时传输和通信管理。

## 基础信息

- **基础路径**: `/mqtt`
- **Content-Type**: `application/json`

## 通用响应格式

所有接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "success", 
  "data": {}
}
```

## 接口详情

### 1. 新建MQTT连接

**功能描述**: 创建新的MQTT连接配置，用于航天器数据传输通道的建立

**请求信息**:
- **端点路径**: `POST /mqtt/new`
- **HTTP方法**: POST

**请求参数**:
```json
{
  "name": "航天器A-遥测数据连接",
  "ip": "*************", 
  "port": 1883,
  "topic": "spacecraft/telemetry/data",
  "userName": "mqtt_user",
  "password": "mqtt_password",
  "note": "用于航天器A的遥测数据传输",
  "status": true
}
```

**参数说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | String | 是 | 连接名称 |
| ip | String | 是 | MQTT服务器IP地址 |
| port | Integer | 是 | MQTT服务器端口 |
| topic | String | 是 | MQTT主题 |
| userName | String | 是 | 用户名 |
| password | String | 是 | 密码 |
| note | String | 否 | 备注信息 |
| status | Boolean | 是 | 启用状态 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "航天器A-遥测数据连接",
    "ip": "*************",
    "port": 1883,
    "topic": "spacecraft/telemetry/data",
    "userName": "mqtt_user",
    "password": "mqtt_password",
    "note": "用于航天器A的遥测数据传输",
    "status": true,
    "updatePerson": "admin",
    "updateTime": "2024-01-15T10:30:00"
  }
}
```

### 2. 测试MQTT连接

**功能描述**: 测试MQTT连接配置的可用性，验证航天器数据传输通道是否正常

**请求信息**:
- **端点路径**: `POST /mqtt/test`
- **HTTP方法**: POST

**请求参数**:
```json
{
  "name": "航天器A-遥测数据连接",
  "ip": "*************",
  "port": 1883,
  "topic": "spacecraft/telemetry/data", 
  "userName": "mqtt_user",
  "password": "mqtt_password",
  "status": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "connected": true,
    "timeCostMs": 150,
    "mqttVersion": "3.1.1",
    "errorMsg": null
  }
}
```

**连接失败响应示例**:
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "connected": false,
    "timeCostMs": 5000,
    "mqttVersion": null,
    "errorMsg": "连接超时：无法连接到指定的MQTT服务器"
  }
}
```

### 3. 更新MQTT连接

**功能描述**: 更新已有的MQTT连接配置信息

**请求信息**:
- **端点路径**: `PUT /mqtt/update`
- **HTTP方法**: PUT

**请求参数**:
```json
{
  "id": 1,
  "name": "航天器A-遥测数据连接(更新)",
  "ip": "*************",
  "port": 1884,
  "topic": "spacecraft/telemetry/data/v2",
  "userName": "mqtt_user_new", 
  "password": "mqtt_password_new",
  "note": "更新后的航天器A遥测数据传输配置",
  "status": true
}
```

**参数说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | Long | 是 | 配置ID |
| 其他字段 | - | 是 | 同新建接口 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "航天器A-遥测数据连接(更新)",
    "ip": "*************",
    "port": 1884,
    "topic": "spacecraft/telemetry/data/v2",
    "userName": "mqtt_user_new",
    "password": "mqtt_password_new", 
    "note": "更新后的航天器A遥测数据传输配置",
    "status": true,
    "updatePerson": "admin",
    "updateTime": "2024-01-15T14:20:00"
  }
}
```

### 4. 删除MQTT连接

**功能描述**: 删除指定的MQTT连接配置

**请求信息**:
- **端点路径**: `DELETE /mqtt/delete`
- **HTTP方法**: DELETE

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | Long | 是 | 配置ID |

**请求示例**: `DELETE /mqtt/delete?id=1`

**响应示例**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

**错误响应示例**:
```json
{
  "code": 500,
  "message": "删除失败",
  "data": null
}
```

### 5. 查询MQTT连接详情

**功能描述**: 根据ID获取指定MQTT连接的详细配置信息

**请求信息**:
- **端点路径**: `GET /mqtt/{id}`
- **HTTP方法**: GET

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | Long | 是 | 配置ID |

**请求示例**: `GET /mqtt/1`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "航天器A-遥测数据连接",
    "ip": "*************",
    "port": 1883,
    "topic": "spacecraft/telemetry/data",
    "userName": "mqtt_user",
    "password": "mqtt_password",
    "note": "用于航天器A的遥测数据传输",
    "status": true,
    "updatePerson": "admin",
    "updateTime": "2024-01-15T10:30:00"
  }
}
```

**配置不存在响应示例**:
```json
{
  "code": 500,
  "message": "配置不存在",
  "data": null
}
```

### 6. 分页查询MQTT连接列表

**功能描述**: 分页获取MQTT连接配置列表，支持按状态筛选

**请求信息**:
- **端点路径**: `GET /mqtt`
- **HTTP方法**: GET

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| pageNum | Integer | 否 | 1 | 页码 |
| pageSize | Integer | 否 | 10 | 每页大小 |
| status | Boolean | 否 | - | 状态筛选(true:启用,false:禁用) |

**请求示例**: `GET /mqtt?pageNum=1&pageSize=10&status=true`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "list": [
      {
        "id": 1,
        "name": "航天器A-遥测数据连接",
        "ip": "*************",
        "port": 1883,
        "topic": "spacecraft/telemetry/data",
        "userName": "mqtt_user",
        "password": "mqtt_password",
        "note": "用于航天器A的遥测数据传输",
        "status": true,
        "updatePerson": "admin",
        "updateTime": "2024-01-15T10:30:00"
      },
      {
        "id": 2,
        "name": "航天器B-指令控制连接",
        "ip": "*************", 
        "port": 1883,
        "topic": "spacecraft/command/control",
        "userName": "mqtt_user2",
        "password": "mqtt_password2",
        "note": "用于航天器B的指令控制传输",
        "status": true,
        "updatePerson": "admin",
        "updateTime": "2024-01-15T11:15:00"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要有效的用户认证
2. MQTT连接配置中的密码等敏感信息会在响应中返回，请注意安全防护
3. 测试连接接口会真实连接到指定的MQTT服务器，请确保网络可达性
4. 删除操作不可逆，请谨慎操作
5. 建议在生产环境中对MQTT连接进行加密传输配置 