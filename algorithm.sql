CREATE TABLE `algorithm` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(48)  NOT NULL COMMENT '算法名称',
    `direction`     VARCHAR(32)  NOT NULL COMMENT '擅长方向',
    `description`   VARCHAR(255) DEFAULT NULL COMMENT '介绍',
    `type`          TINYINT      NOT NULL COMMENT '算法类型：1-类型A 2-类型B 3-类型C',
    `enabled`       BOOLEAN      DEFAULT TRUE COMMENT '启用状态',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='算法表';