import request from '@/utils/request'

/**
 * 航天器信息搜索
 * @param {Object} params 搜索参数
 * @param {string} params.model 型号，模糊匹配
 * @param {string} params.name 名称，模糊匹配
 * @param {string} params.header 负责人，模糊匹配
 * @param {string} params.company 研制单位，模糊匹配
 * @param {number} params.pageNo 页码，默认1
 * @param {number} params.pageSize 每页条数，默认10
 * @returns {Promise} 返回搜索结果
 */
export function craftSearch(params) {
  return request({
    url: '/single/craftsearch',
    method: 'get',
    params
  })
}
