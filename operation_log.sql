CREATE TABLE `operation_log` (
  `id`            BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `request_time`  DATETIME      NOT NULL COMMENT '请求时间',
  `operator`      VARCHAR(16)   NOT NULL COMMENT '操作人',
  `title`         VARCHAR(32)   NOT NULL COMMENT '操作标题',
  `request_url`   VARCHAR(255)  NOT NULL COMMENT '请求地址',
  `request_method` VARCHAR(10)  NOT NULL COMMENT '请求方式 GET/POST...',
  `ip_address`    VARBINARY(16) NOT NULL COMMENT 'IP地址',
‘client’       VARCHAR(16)      NOT NULL    COMMENT '客户端',
  `user_agent`    VARCHAR(512)  DEFAULT NULL COMMENT '浏览器 UA',
  `duration_ms`   INT           NOT NULL COMMENT '耗时(毫秒)',
  `request_param` JSON          DEFAULT NULL COMMENT '请求参数 JSON',
  `create_time`   DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表';