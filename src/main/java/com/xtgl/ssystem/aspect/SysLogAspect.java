package com.xtgl.ssystem.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xtgl.ssystem.common.annotation.WebLog;
import com.xtgl.ssystem.common.dto.OptLogDTO;
import com.xtgl.ssystem.common.event.SysLogEvent;
import com.xtgl.ssystem.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

/**
 * 系统日志切面
 */
@Slf4j
@Aspect
@Component
public class SysLogAspect {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 定义切点：所有带有@WebLog注解的方法
     */
    @Pointcut("@annotation(com.xtgl.ssystem.common.annotation.WebLog)")
    public void webLogPointcut() {
    }

    /**
     * 环绕通知：记录操作日志
     */
    @Around("webLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        WebLog webLog = method.getAnnotation(WebLog.class);
        
        Object result = null;
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } finally {
            // 记录日志
            recordLog(joinPoint, webLog, request, startTime);
        }
    }

    /**
     * 记录操作日志
     */
    private void recordLog(ProceedingJoinPoint joinPoint, WebLog webLog, HttpServletRequest request, long startTime) {
        try {
            OptLogDTO logDto = new OptLogDTO();
            
            // 设置基本信息
            logDto.setRequestTime(LocalDateTime.now());
            logDto.setTitle(webLog.title());
            
            if (request != null) {
                logDto.setRequestUrl(request.getRequestURI());
                logDto.setRequestMethod(request.getMethod());

                String clientIp = IpUtil.getClientIpAddress(request);
                log.debug("SysLogAspect - 获取到的客户端IP: {}", clientIp);
                logDto.setIpAddress(clientIp);

                logDto.setUserAgent(request.getHeader("User-Agent"));
                logDto.setClient("Web");
                
                // 获取操作人（这里简化处理，实际项目中应该从认证信息中获取）
                String operator = request.getHeader("X-User-Name");
                if (operator == null || operator.isEmpty()) {
                    operator = "系统用户";
                }
                logDto.setOperator(operator);
                
                // 记录请求参数
                if (webLog.recordParam()) {
                    Object[] args = joinPoint.getArgs();
                    if (args != null && args.length > 0) {
                        try {
                            logDto.setRequestParam(objectMapper.writeValueAsString(args));
                        } catch (Exception e) {
                            log.warn("序列化请求参数失败", e);
                            logDto.setRequestParam("参数序列化失败");
                        }
                    }
                }
            } else {
                // 非HTTP请求的默认值
                logDto.setRequestUrl("非HTTP请求");
                logDto.setRequestMethod("UNKNOWN");
                logDto.setIpAddress("127.0.0.1");
                logDto.setClient("System");
                logDto.setOperator("系统");
            }
            
            // 计算耗时
            long endTime = System.currentTimeMillis();
            logDto.setDurationMs((int) (endTime - startTime));
            
            // 发布异步事件
            eventPublisher.publishEvent(new SysLogEvent(this, logDto));
            
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }
}
