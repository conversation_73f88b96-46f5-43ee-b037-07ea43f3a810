package com.xtgl.ssystem.common.dto;

import lombok.Data;

/**
 * 算法DTO
 */
@Data
public class AlgorithmDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 算法名称
     */
    private String name;

    /**
     * 擅长方向
     */
    private String direction;

    /**
     * 介绍
     */
    private String description;

    /**
     * 算法类型：1-无监督算法 2-监督算法 3-深度学习算法
     */
    private Integer type;

    /**
     * 算法类型名称
     */
    private String typeName;

    /**
     * 启用状态
     */
    private Boolean enabled;

    /**
     * 启用状态文本
     */
    private String enabledText;
}
