package com.xtgl.ssystem.common.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 模块DTO
 */
@Data
public class ModuleDto {

    /**
     * 模块名称
     */
    @NotBlank(message = "模块名称不能为空")
    private String name;

    /**
     * 所属分系统ID
     */
    @NotNull(message = "分系统ID不能为空")
    private Long subsystemId;

    /**
     * 所属航天器ID
     */
    @NotNull(message = "航天器ID不能为空")
    private Long spacecraftId;

    /**
     * 所属单机ID
     */
    @NotNull(message = "单机ID不能为空")
    private Long singleId;
}
