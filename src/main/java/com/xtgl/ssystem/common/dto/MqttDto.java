package com.xtgl.ssystem.common.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * MQTT配置数据传输对象
 */
@Data
public class MqttDto {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "连接名称不能为空")
    private String name;

    /**
     * IP地址 - 接收前端传入的字符串格式
     */
    @NotBlank(message = "IP地址不能为空")
    private String ip;

    /**
     * 端口
     */
    @NotNull(message = "端口不能为空")
    private Integer port;

    /**
     * 主题
     */
    @NotBlank(message = "主题不能为空")
    private String topic;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 备注
     */
    private String note;

    /**
     * 启用状态
     */
    @NotNull(message = "启用状态不能为空")
    private Boolean status;

    /**
     * 更新人
     */
    private String updatePerson;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 