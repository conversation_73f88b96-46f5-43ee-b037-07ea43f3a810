package com.xtgl.ssystem.common.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 操作日志搜索参数传输对象
 */
@Data
public class OperationLogSearchDTO {

    /**
     * 操作人姓名（模糊匹配）
     */
    private String userName;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 页码，默认1
     */
    private Integer pageNo = 1;

    /**
     * 每页条数，默认10
     */
    private Integer pageSize = 10;
}
