package com.xtgl.ssystem.common.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 操作日志数据传输对象
 */
@Data
public class OptLogDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 请求时间
     */
    @NotNull(message = "请求时间不能为空")
    private LocalDateTime requestTime;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

    /**
     * 操作标题
     */
    @NotBlank(message = "操作标题不能为空")
    private String title;

    /**
     * 请求地址
     */
    @NotBlank(message = "请求地址不能为空")
    private String requestUrl;

    /**
     * 请求方式 GET/POST...
     */
    @NotBlank(message = "请求方式不能为空")
    private String requestMethod;

    /**
     * IP地址
     */
    @NotBlank(message = "IP地址不能为空")
    private String ipAddress;

    /**
     * 客户端
     */
    @NotBlank(message = "客户端不能为空")
    private String client;

    /**
     * 浏览器 UA
     */
    private String userAgent;

    /**
     * 耗时(毫秒)
     */
    @NotNull(message = "耗时不能为空")
    private Integer durationMs;

    /**
     * 请求参数 JSON
     */
    private String requestParam;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;
}
