package com.xtgl.ssystem.common.dto;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 航天器数据传输对象
 */
@Data
public class SatelliteDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 型号
     */
    @NotBlank(message = "型号不能为空")
    private String model;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 负责人
     */
    @NotBlank(message = "负责人不能为空")
    private String header;

    /**
     * 创建时间
     */
    private LocalDate createTime;

    /**
     * 所属单位
     */
    @NotBlank(message = "所属单位不能为空")
    private String company;

    /**
     * 开始接收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 卫星状态
     */
    private String status;

    /**
     * 批次
     */
    @NotNull(message = "批次不能为空")
    @Min(value = 1, message = "批次必须大于等于1")
    private Integer batch;

    /**
     * MQTT名称
     */
    @NotBlank(message = "MQTT名称不能为空")
    private String mqttName;


} 