package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 算法实体类
 */
@Data
@TableName("algorithm")
public class Algorithm {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 算法名称
     */
    @TableField("name")
    private String name;

    /**
     * 擅长方向
     */
    @TableField("direction")
    private String direction;

    /**
     * 介绍
     */
    @TableField("description")
    private String description;

    /**
     * 算法类型：1-无监督算法 2-监督算法 3-深度学习算法
     */
    @TableField("type")
    private Integer type;

    /**
     * 启用状态
     */
    @TableField("enabled")
    private Boolean enabled;
}
