package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 模块实体类
 */
@Data
@TableName("module")
public class Module {

    /**
     * 模块ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模块名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属分系统ID
     */
    @TableField("subsystem_id")
    private Long subsystemId;

    /**
     * 所属航天器ID
     */
    @TableField("spacecraft_id")
    private Long spacecraftId;

    /**
     * 所属单机ID
     */
    @TableField("single_id")
    private Long singleId;
}
