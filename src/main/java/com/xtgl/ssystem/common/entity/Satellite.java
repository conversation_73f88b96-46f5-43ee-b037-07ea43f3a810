package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 航天器信息实体类
 */
@Data
@TableName("satellite")
public class Satellite {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 型号
     */
    @TableField("model")
    private String model;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 负责人
     */
    @TableField("header")
    private String header;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDate createTime;

    /**
     * 所属单位
     */
    @TableField("company")
    private String company;

    /**
     * 开始接收时间
     */
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    /**
     * 卫星状态
     */
    @TableField("status")
    private String status;

    /**
     * 批次
     */
    @TableField("batch")
    private Integer batch;

    /**
     * MQTT名称
     */
    @TableField("mqtt_name")
    private String mqttName;
    

} 