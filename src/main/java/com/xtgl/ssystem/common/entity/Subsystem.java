package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 分系统实体类
 */
@Data
@TableName("subsystem")
public class Subsystem {

    /**
     * 分系统ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分系统名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属航天器ID
     */
    @TableField("spacecraft_id")
    private Long spacecraftId;
}
