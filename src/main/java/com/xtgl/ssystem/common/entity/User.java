package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户实体类 - 对应tmp_user表
 */
@Data
@TableName("tmp_user")
public class User {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 账号
     */
    private String account;
    
    /**
     * 密码 - 60位bcrypt加密
     */
    private String password;
} 