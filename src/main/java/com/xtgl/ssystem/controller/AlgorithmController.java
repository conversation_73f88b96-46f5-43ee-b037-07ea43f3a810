package com.xtgl.ssystem.controller;

import com.xtgl.ssystem.common.annotation.WebLog;
import com.xtgl.ssystem.common.dto.AlgorithmDto;
import com.xtgl.ssystem.common.dto.AlgorithmSearchDto;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.common.entity.Result;
import com.xtgl.ssystem.service.AlgorithmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 算法控制器
 */
@Slf4j
@RestController
@RequestMapping("/algorithm")
public class AlgorithmController {

    @Autowired
    private AlgorithmService algorithmService;

    /**
     * 根据算法类型或算法名称搜索
     *
     * @param name     算法名称关键字
     * @param type     算法类型枚举
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return 分页结果
     */
    @GetMapping("/search")
    @WebLog(title = "搜索算法", recordParam = false)
    public Result<PageResult<AlgorithmDto>> searchAlgorithms(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {

        log.info("搜索算法请求: name={}, type={}, pageNum={}, pageSize={}", name, type, pageNum, pageSize);

        // 参数验证
        if (pageNum != null && pageNum <= 0) {
            return Result.error(400, "页码必须大于0");
        }
        if (pageSize != null && (pageSize <= 0 || pageSize > 100)) {
            return Result.error(400, "每页条数必须在1-100之间");
        }

        try {
            AlgorithmSearchDto searchDto = new AlgorithmSearchDto();
            searchDto.setName(name);
            searchDto.setType(type);
            searchDto.setPageNum(pageNum);
            searchDto.setPageSize(pageSize);

            PageResult<AlgorithmDto> result = algorithmService.searchAlgorithms(searchDto);
            log.info("搜索算法成功，共{}条记录", result.getTotal());
            return Result.success(result);
        } catch (Exception e) {
            log.error("搜索算法失败", e);
            return Result.error("搜索算法失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取算法
     *
     * @param id 算法ID
     * @return 算法信息
     */
    @GetMapping("/{id}")
    @WebLog(title = "查看算法详情", recordParam = false)
    public Result<AlgorithmDto> getAlgorithmById(@PathVariable Long id) {
        log.info("获取算法详情请求: id={}", id);

        try {
            AlgorithmDto algorithm = algorithmService.getAlgorithmById(id);
            if (algorithm != null) {
                return Result.success(algorithm);
            } else {
                return Result.error(404, "算法不存在");
            }
        } catch (Exception e) {
            log.error("获取算法详情失败", e);
            return Result.error("获取算法详情失败：" + e.getMessage());
        }
    }

    /**
     * 修改算法信息（只能更新启停状态）
     *
     * @param id      算法ID
     * @param enabled 启用状态
     * @return 更新结果
     */
    @PutMapping("/update")
    @WebLog(title = "修改算法状态")
    public Result<Void> updateAlgorithm(@RequestParam Long id, @RequestParam Boolean enabled) {
        log.info("更新算法状态请求: id={}, enabled={}", id, enabled);

        // 参数验证
        if (id == null || id <= 0) {
            return Result.error(400, "算法ID不能为空且必须大于0");
        }
        if (enabled == null) {
            return Result.error(400, "启用状态不能为空");
        }

        try {
            boolean success = algorithmService.updateAlgorithmStatus(id, enabled);
            if (success) {
                log.info("更新算法状态成功");
                return Result.success();
            } else {
                return Result.error("更新算法状态失败，可能算法不存在");
            }
        } catch (Exception e) {
            log.error("更新算法状态失败", e);
            return Result.error("更新算法状态失败：" + e.getMessage());
        }
    }
}
