package com.xtgl.ssystem.controller;

import com.xtgl.ssystem.common.annotation.WebLog;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.common.entity.Result;
import com.xtgl.ssystem.common.dto.MqttDto;
import com.xtgl.ssystem.common.dto.MqttTestResultDto;
import com.xtgl.ssystem.service.MqttService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * MQTT控制器
 */
@RestController
@RequestMapping("/mqtt")
public class MqttController {

    @Autowired
    private MqttService mqttService;

    /**
     * 新建MQTT连接
     *
     * @param mqttDto MQTT配置DTO
     * @return 新建的MQTT配置DTO，包含ID
     */
    @PostMapping("/new")
    @WebLog(title = "新建MQTT连接")
    public Result<MqttDto> createMqtt(@RequestBody @Validated MqttDto mqttDto) {
        MqttDto result = mqttService.createMqtt(mqttDto);
        return Result.success(result);
    }

    /**
     * 测试MQTT连接
     *
     * @param mqttDto MQTT配置DTO
     * @return 连接测试结果
     */
    @PostMapping("/test")
    @WebLog(title = "测试MQTT连接")
    public Result<MqttTestResultDto> testMqttConnection(@RequestBody @Validated MqttDto mqttDto) {
        MqttTestResultDto result = mqttService.testMqttConnection(mqttDto);
        return Result.success(result);
    }

    /**
     * 更新MQTT连接
     *
     * @param mqttDto MQTT配置DTO
     * @return 更新后的MQTT配置DTO
     */
    @PutMapping("/update")
    @WebLog(title = "修改MQTT连接")
    public Result<MqttDto> updateMqtt(@RequestBody @Validated MqttDto mqttDto) {
        if (mqttDto.getId() == null) {
            return Result.error("ID不能为空");
        }
        MqttDto result = mqttService.updateMqtt(mqttDto);
        return Result.success(result);
    }

    /**
     * 删除MQTT连接
     *
     * @param id 配置ID
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    @WebLog(title = "删除MQTT连接")
    public Result<Void> deleteMqtt(@RequestParam("id") Long id) {
        if (id == null) {
            return Result.error("ID不能为空");
        }
        boolean success = mqttService.deleteMqtt(id);
        if (success) {
            return Result.success("删除成功", null);
        } else {
            return Result.error("删除失败");
        }
    }

    /**
     * 根据ID获取MQTT连接配置
     *
     * @param id 配置ID
     * @return MQTT配置DTO
     */
    @GetMapping("/{id}")
    @WebLog(title = "查看MQTT连接详情", recordParam = false)
    public Result<MqttDto> getMqttById(@PathVariable("id") Long id) {
        MqttDto mqttDto = mqttService.getMqttById(id);
        if (mqttDto != null) {
            return Result.success(mqttDto);
        } else {
            return Result.error("配置不存在");
        }
    }

    /**
     * 分页查询MQTT连接配置
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param status   状态筛选
     * @return 分页结果
     */
    @GetMapping
    @WebLog(title = "查询MQTT连接列表", recordParam = false)
    public Result<PageResult<MqttDto>> pageMqtt(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "status", required = false) Boolean status) {
        PageResult<MqttDto> pageResult = mqttService.pageMqtt(pageNum, pageSize, status);
        return Result.success(pageResult);
    }
} 