package com.xtgl.ssystem.controller;

import com.xtgl.ssystem.common.annotation.WebLog;
import com.xtgl.ssystem.common.dto.*;
import com.xtgl.ssystem.common.entity.*;
import com.xtgl.ssystem.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单机配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/single")
public class SingleController {

    @Autowired
    private SatelliteService satelliteService;

    @Autowired
    private SubsystemService subsystemService;

    @Autowired
    private SingleService singleService;

    @Autowired
    private ModuleService moduleService;

    @Autowired
    private TelemetryBindService telemetryBindService;

    /**
     * 航天器信息搜索
     * 
     * 功能描述：
     * 在"航天器信息展示"基础上增加多条件过滤能力，内部转发至 /satellite/search 完成检索。
     * 支持型号、名称、负责人、研制单位四个维度的模糊/精确查询，查询结果保持相同的分页结构。
     * 若所有条件为空，则退化为"展示全部"。
     *
     * @param model    型号，可精确匹配
     * @param name     名称，模糊匹配
     * @param header   负责人，模糊匹配
     * @param company  研制单位，模糊匹配
     * @param pageNo   页码，默认1
     * @param pageSize 每页条数，默认10
     * @return 分页结果，格式与SatelliteController保持一致但字段名调整
     */
    @GetMapping("/craftsearch")
    @WebLog(title = "搜索航天器信息", recordParam = false)
    public Result<Map<String, Object>> craftSearch(
            @RequestParam(value = "model", required = false) String model,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "header", required = false) String header,
            @RequestParam(value = "company", required = false) String company,
            @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        
        // 构建搜索条件DTO
        SatelliteSearchDto searchDto = new SatelliteSearchDto();
        searchDto.setModel(model);
        searchDto.setName(name);
        searchDto.setHeader(header);
        searchDto.setCompany(company);
        searchDto.setPageNo(pageNo);
        searchDto.setPageSize(pageSize);
        
        // 调用SatelliteService进行搜索，确保数据一致性
        PageResult<?> pageResult = satelliteService.searchSatellites(searchDto);

        // 转换响应格式以符合接口规范
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("total", pageResult.getTotal());
        responseData.put("list", pageResult.getList());
        responseData.put("page", pageNo);
        responseData.put("size", pageSize);
        
        return Result.success(responseData);
    }

    // ==================== 分系统管理 ====================

    /**
     * 给指定卫星添加分系统
     */
    @PostMapping("/subsystemadd")
    @WebLog(title = "添加分系统")
    public Result<?> addSubsystem(@RequestBody @Validated SubsystemDto subsystemDto) {
        log.info("添加分系统请求: {}", subsystemDto);
        try {
            Long subsystemId = subsystemService.addSubsystem(subsystemDto);
            log.info("分系统添加成功，ID: {}", subsystemId);
            return Result.success(subsystemId);
        } catch (Exception e) {
            log.error("添加分系统失败", e);
            return Result.error("添加分系统失败：" + e.getMessage());
        }
    }

    /**
     * 查询某卫星下的所有分系统（第1级）
     */
    @GetMapping("/satellite/{satelliteId}/subsystems")
    @WebLog(title = "查询分系统列表", recordParam = false)
    public Result<List<Subsystem>> getSubsystemsBySatelliteId(@PathVariable Long satelliteId) {
        log.info("查询航天器{}下的分系统", satelliteId);
        try {
            List<Subsystem> subsystems = subsystemService.getSubsystemsBySatelliteId(satelliteId);
            log.info("查询到{}个分系统", subsystems.size());
            return Result.success(subsystems);
        } catch (Exception e) {
            log.error("查询分系统失败", e);
            return Result.error("查询分系统失败：" + e.getMessage());
        }
    }

    /**
     * 删除指定分系统（级联删除其下所有单机及模块）
     */
    @DeleteMapping("/subsystems/{subsystemId}")
    @WebLog(title = "删除分系统")
    public Result<?> deleteSubsystem(@PathVariable Long subsystemId) {
        log.info("删除分系统请求: {}", subsystemId);
        try {
            subsystemService.deleteSubsystem(subsystemId);
            log.info("分系统删除成功");
            return Result.success();
        } catch (Exception e) {
            log.error("删除分系统失败", e);
            return Result.error("删除分系统失败：" + e.getMessage());
        }
    }

    /**
     * 编辑指定分系统
     */
    @PutMapping("/subsystems/{subsystemId}")
    @WebLog(title = "编辑分系统")
    public Result<?> updateSubsystem(@PathVariable Long subsystemId,
                                   @RequestBody @Validated SubsystemDto subsystemDto) {
        log.info("编辑分系统请求: ID={}, data={}", subsystemId, subsystemDto);
        try {
            subsystemService.updateSubsystem(subsystemId, subsystemDto);
            log.info("分系统编辑成功");
            return Result.success();
        } catch (Exception e) {
            log.error("编辑分系统失败", e);
            return Result.error("编辑分系统失败：" + e.getMessage());
        }
    }

    // ==================== 单机管理 ====================

    /**
     * 给指定分系统添加单机
     */
    @PostMapping("/singleadd")
    @WebLog(title = "添加单机")
    public Result<?> addSingle(@RequestBody @Validated SingleDto singleDto) {
        log.info("添加单机请求: {}", singleDto);
        try {
            Long singleId = singleService.addSingle(singleDto);
            log.info("单机添加成功，ID: {}", singleId);
            return Result.success(singleId);
        } catch (Exception e) {
            log.error("添加单机失败", e);
            return Result.error("添加单机失败：" + e.getMessage());
        }
    }

    /**
     * 查询某分系统下的所有单机（第2级）
     */
    @GetMapping("/subsystem/{subsystemId}/singles")
    @WebLog(title = "查询单机列表", recordParam = false)
    public Result<List<Single>> getSinglesBySubsystemId(@PathVariable Long subsystemId) {
        log.info("查询分系统{}下的单机", subsystemId);
        try {
            List<Single> singles = singleService.getSinglesBySubsystemId(subsystemId);
            log.info("查询到{}个单机", singles.size());
            return Result.success(singles);
        } catch (Exception e) {
            log.error("查询单机失败", e);
            return Result.error("查询单机失败：" + e.getMessage());
        }
    }

    /**
     * 删除指定单机（级联删除其下所有模块）
     */
    @DeleteMapping("/singles/{singleId}")
    @WebLog(title = "删除单机")
    public Result<?> deleteSingle(@PathVariable Long singleId) {
        log.info("删除单机请求: {}", singleId);
        try {
            singleService.deleteSingle(singleId);
            log.info("单机删除成功");
            return Result.success();
        } catch (Exception e) {
            log.error("删除单机失败", e);
            return Result.error("删除单机失败：" + e.getMessage());
        }
    }

    /**
     * 编辑指定单机
     */
    @PutMapping("/singles/{singleId}")
    @WebLog(title = "编辑单机")
    public Result<?> updateSingle(@PathVariable Long singleId,
                                @RequestBody @Validated SingleDto singleDto) {
        log.info("编辑单机请求: ID={}, data={}", singleId, singleDto);
        try {
            singleService.updateSingle(singleId, singleDto);
            log.info("单机编辑成功");
            return Result.success();
        } catch (Exception e) {
            log.error("编辑单机失败", e);
            return Result.error("编辑单机失败：" + e.getMessage());
        }
    }

    // ==================== 模块管理 ====================

    /**
     * 给指定单机添加模块
     */
    @PostMapping("/moduleadd")
    @WebLog(title = "添加模块")
    public Result<?> addModule(@RequestBody @Validated ModuleDto moduleDto) {
        log.info("添加模块请求: {}", moduleDto);
        try {
            Long moduleId = moduleService.addModule(moduleDto);
            log.info("模块添加成功，ID: {}", moduleId);
            return Result.success(moduleId);
        } catch (Exception e) {
            log.error("添加模块失败", e);
            return Result.error("添加模块失败：" + e.getMessage());
        }
    }

    /**
     * 查询某单机下的所有模块（第3级）
     */
    @GetMapping("/single/{singleId}/modules")
    @WebLog(title = "查询模块列表", recordParam = false)
    public Result<List<com.xtgl.ssystem.common.entity.Module>> getModulesBySingleId(@PathVariable Long singleId) {
        log.info("查询单机{}下的模块", singleId);
        try {
            List<com.xtgl.ssystem.common.entity.Module> modules = moduleService.getModulesBySingleId(singleId);
            log.info("查询到{}个模块", modules.size());
            return Result.success(modules);
        } catch (Exception e) {
            log.error("查询模块失败", e);
            return Result.error("查询模块失败：" + e.getMessage());
        }
    }

    /**
     * 删除指定模块
     */
    @DeleteMapping("/modules/{moduleId}")
    @WebLog(title = "删除模块")
    public Result<?> deleteModule(@PathVariable Long moduleId) {
        log.info("删除模块请求: {}", moduleId);
        try {
            moduleService.deleteModule(moduleId);
            log.info("模块删除成功");
            return Result.success();
        } catch (Exception e) {
            log.error("删除模块失败", e);
            return Result.error("删除模块失败：" + e.getMessage());
        }
    }

    /**
     * 编辑指定模块
     */
    @PutMapping("/modules/{moduleId}")
    @WebLog(title = "编辑模块")
    public Result<?> updateModule(@PathVariable Long moduleId,
                                @RequestBody @Validated ModuleDto moduleDto) {
        log.info("编辑模块请求: ID={}, data={}", moduleId, moduleDto);
        try {
            moduleService.updateModule(moduleId, moduleDto);
            log.info("模块编辑成功");
            return Result.success();
        } catch (Exception e) {
            log.error("编辑模块失败", e);
            return Result.error("编辑模块失败：" + e.getMessage());
        }
    }

    // ==================== 遥测代号绑定相关接口 ====================

    /**
     * 绑定遥测代号
     */
    @PostMapping("/telemetry/bind")
    @WebLog(title = "绑定遥测代号")
    public Result<TelemetryBindResponseDto> bindTelemetryCode(@RequestBody @Validated TelemetryBindDto bindDto) {
        log.info("绑定遥测代号请求: {}", bindDto);
        try {
            TelemetryBindResponseDto response = telemetryBindService.bindTelemetryCode(bindDto);
            log.info("遥测代号绑定成功");
            return Result.success(response);
        } catch (Exception e) {
            log.error("绑定遥测代号失败", e);
            if (e.getMessage().contains("父级未绑定")) {
                return Result.error(409, e.getMessage());
            } else if (e.getMessage().contains("已绑定")) {
                return Result.error(409, e.getMessage());
            } else if (e.getMessage().contains("不存在")) {
                return Result.error(404, e.getMessage());
            } else {
                return Result.error(e.getMessage());
            }
        }
    }

    /**
     * 查询卫星绑定的遥测代号数据
     */
    @GetMapping("/codesearch")
    @WebLog(title = "查询遥测代号", recordParam = false)
    public Result<List<TelemetryCode>> searchTelemetryCodes(@RequestParam Long satelliteId) {
        log.info("查询卫星遥测代号请求: satelliteId={}", satelliteId);
        try {
            List<TelemetryCode> telemetryCodes = telemetryBindService.getBoundTelemetryCodes("satellite", satelliteId);
            log.info("查询到{}条遥测代号", telemetryCodes.size());
            return Result.success(telemetryCodes);
        } catch (Exception e) {
            log.error("查询遥测代号失败", e);
            return Result.error("查询遥测代号失败：" + e.getMessage());
        }
    }

    /**
     * 查询指定层级绑定的遥测代号
     */
    @GetMapping("/telemetry/bound")
    @WebLog(title = "查询已绑定遥测代号", recordParam = false)
    public Result<List<TelemetryCode>> getBoundTelemetryCodes(
            @RequestParam String level,
            @RequestParam Long id) {
        log.info("查询绑定遥测代号请求: level={}, id={}", level, id);
        try {
            List<TelemetryCode> telemetryCodes = telemetryBindService.getBoundTelemetryCodes(level, id);
            log.info("查询到{}条绑定遥测代号", telemetryCodes.size());
            return Result.success(telemetryCodes);
        } catch (Exception e) {
            log.error("查询绑定遥测代号失败", e);
            return Result.error("查询绑定遥测代号失败：" + e.getMessage());
        }
    }

    /**
     * 查询可绑定的遥测代号（根据层级继承规则）
     */
    @GetMapping("/telemetry/available")
    @WebLog(title = "查询可绑定遥测代号", recordParam = false)
    public Result<List<TelemetryCode>> getAvailableTelemetryCodes(
            @RequestParam String level,
            @RequestParam Long id) {
        log.info("查询可绑定遥测代号请求: level={}, id={}", level, id);
        try {
            List<TelemetryCode> telemetryCodes = telemetryBindService.getAvailableTelemetryCodes(level, id);
            log.info("查询到{}条可绑定遥测代号", telemetryCodes.size());
            return Result.success(telemetryCodes);
        } catch (Exception e) {
            log.error("查询可绑定遥测代号失败", e);
            return Result.error("查询可绑定遥测代号失败：" + e.getMessage());
        }
    }

    /**
     * 删除遥测代号绑定
     */
    @DeleteMapping("/telemetry/unbind")
    @WebLog(title = "解绑遥测代号")
    public Result<?> unbindTelemetryCode(
            @RequestParam String level,
            @RequestParam Long id) {
        log.info("删除遥测代号绑定请求: level={}, id={}", level, id);
        try {
            int count = telemetryBindService.unbindTelemetryCode(level, id);
            log.info("删除了{}条遥测代号绑定", count);
            return Result.success();
        } catch (Exception e) {
            log.error("删除遥测代号绑定失败", e);
            return Result.error("删除遥测代号绑定失败：" + e.getMessage());
        }
    }

    /**
     * 删除特定的遥测代号绑定记录
     */
    @DeleteMapping("/telemetry/unbind/{telemetryCodeId}")
    @WebLog(title = "删除特定遥测代号绑定")
    public Result<?> unbindSpecificTelemetryCode(@PathVariable Long telemetryCodeId) {
        log.info("删除特定遥测代号绑定请求: telemetryCodeId={}", telemetryCodeId);
        try {
            int count = telemetryBindService.unbindSpecificTelemetryCode(telemetryCodeId);
            log.info("删除了{}条遥测代号绑定", count);
            return Result.success();
        } catch (Exception e) {
            log.error("删除特定遥测代号绑定失败", e);
            return Result.error("删除特定遥测代号绑定失败：" + e.getMessage());
        }
    }

    /**
     * 根据层级和遥测代号序号删除绑定
     */
    @DeleteMapping("/telemetry/unbind-by-serial")
    @WebLog(title = "按序号解绑遥测代号")
    public Result<?> unbindTelemetryCodeBySerialNum(
            @RequestParam String level,
            @RequestParam Long levelId,
            @RequestParam Integer serialNum) {
        log.info("根据层级和序号删除遥测代号绑定请求: level={}, levelId={}, serialNum={}", level, levelId, serialNum);
        try {
            int count = telemetryBindService.unbindTelemetryCodeBySerialNum(level, levelId, serialNum);
            log.info("删除了{}条遥测代号绑定", count);
            return Result.success();
        } catch (Exception e) {
            log.error("根据层级和序号删除遥测代号绑定失败", e);
            return Result.error("根据层级和序号删除遥测代号绑定失败：" + e.getMessage());
        }
    }
}
