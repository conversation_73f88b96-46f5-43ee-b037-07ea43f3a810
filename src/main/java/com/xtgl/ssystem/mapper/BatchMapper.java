package com.xtgl.ssystem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xtgl.ssystem.common.entity.Batch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 批次Mapper接口
 */
@Mapper
public interface BatchMapper extends BaseMapper<Batch> {

    /**
     * 批量插入批次
     *
     * @param batches 批次列表
     * @return 插入数量
     */
    int batchInsert(List<Integer> batches);
    
    /**
     * 查询所有批次号（按降序排列）
     * 
     * @return 批次号列表
     */
    List<Integer> selectAllBatchNames();
} 