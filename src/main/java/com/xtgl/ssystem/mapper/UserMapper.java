package com.xtgl.ssystem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xtgl.ssystem.common.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 用户数据访问层
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据账号查找用户
     */
    @Select("SELECT id, account, password FROM tmp_user WHERE account = #{account}")
    User findByAccount(String account);
} 