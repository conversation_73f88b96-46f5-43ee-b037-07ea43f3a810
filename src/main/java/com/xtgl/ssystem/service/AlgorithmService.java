package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.dto.AlgorithmDto;
import com.xtgl.ssystem.common.dto.AlgorithmSearchDto;
import com.xtgl.ssystem.common.entity.PageResult;

/**
 * 算法服务接口
 */
public interface AlgorithmService {

    /**
     * 分页搜索算法
     *
     * @param searchDto 搜索条件
     * @return 分页结果
     */
    PageResult<AlgorithmDto> searchAlgorithms(AlgorithmSearchDto searchDto);

    /**
     * 根据ID获取算法
     *
     * @param id 算法ID
     * @return 算法DTO
     */
    AlgorithmDto getAlgorithmById(Long id);

    /**
     * 更新算法启用状态
     *
     * @param id      算法ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    boolean updateAlgorithmStatus(Long id, Boolean enabled);
}
