package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.dto.OptLogDTO;
import com.xtgl.ssystem.common.dto.OperationLogSearchDTO;
import com.xtgl.ssystem.common.entity.PageResult;

/**
 * 操作日志服务接口
 */
public interface OperationLogService {

    /**
     * 记录操作日志
     *
     * @param logDto 日志信息
     */
    void saveOperationLog(OptLogDTO logDto);

    /**
     * 分页搜索操作日志
     *
     * @param searchDto 搜索条件
     * @return 分页结果
     */
    PageResult<OptLogDTO> searchOperationLogs(OperationLogSearchDTO searchDto);
}
