package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.dto.MqttTestResultDto;
import com.xtgl.ssystem.common.dto.SatelliteDto;
import com.xtgl.ssystem.common.dto.SatelliteSearchDto;
import com.xtgl.ssystem.common.dto.TelemetryCodeDto;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.common.entity.TelemetryCode;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 航天器服务接口
 */
public interface SatelliteService {

    /**
     * 新建航天器
     *
     * @param satelliteDto 航天器DTO
     * @return 新建的航天器DTO，包含ID和创建时间
     */
    Map<String, Object> createSatellite(SatelliteDto satelliteDto);

    /**
     * 批量删除航天器
     *
     * @param ids 航天器ID列表
     * @param all 是否删除所有
     * @return 删除数量
     */
    int deleteSatellites(List<Long> ids, boolean all);

    /**
     * 分页条件查询航天器
     *
     * @param searchDto 搜索条件
     * @return 分页结果
     */
    PageResult<SatelliteDto> searchSatellites(SatelliteSearchDto searchDto);

    /**
     * 根据ID获取航天器
     *
     * @param id 航天器ID
     * @return 航天器DTO
     */
    SatelliteDto getSatelliteById(Long id);

    /**
     * 更新航天器
     *
     * @param satelliteDto 航天器DTO
     * @return 更新后的航天器DTO
     */
    SatelliteDto updateSatellite(SatelliteDto satelliteDto);

    /**
     * 批量添加批次
     *
     * @param batches 批次列表
     * @return 插入数量
     */
    int addBatches(List<Integer> batches);

    /**
     * 查询所有批次
     *
     * @return 批次列表
     */
    List<Integer> getAllBatches();

    /**
     * 测试MQTT连接
     *
     * @param mqttName MQTT名称
     * @return 连接测试结果
     */
    MqttTestResultDto testMqttConnection(String mqttName);

    /**
     * 获取所有MQTT连接名称
     *
     * @return MQTT连接名称列表
     */
    List<String> getAllMqttNames();

    /**
     * 上传遥测代号数据文件（不绑定航天器）
     *
     * @param file Excel文件
     * @return 上传结果
     */
    Map<String, Object> uploadTelemetryCodeFile(MultipartFile file);

    /**
     * 上传遥测代号数据文件（使用文件名作为批次标识）
     *
     * @param file     Excel文件
     * @param fileName 文件名（批次标识）
     * @return 上传结果
     */
    Map<String, Object> uploadTelemetryCodeFileWithBatch(MultipartFile file, String fileName);
    


    /**
     * 导入遥测代号数据（绑定到航天器）
     *
     * @param file        Excel文件
     * @param satelliteId 航天器ID
     * @return 上传结果
     */
    Map<String, Object> uploadTelemetryCodes(MultipartFile file, Long satelliteId);


    

    


    /**
     * 删除遥测代号数据
     * 
     * @param id 遥测代号ID
     * @return 是否删除成功
     */
    boolean deleteTelemetryCode(Long id);
    
    /**
     * 批量删除遥测代号数据
     * 
     * @param ids 遥测代号ID列表
     * @return 删除的数量
     */
    int deleteTelemetryCodes(List<Long> ids);



    /**
     * 查询遥测代号
     *
     * @param id          遥测代号ID
     * @param satelliteId 航天器ID
     * @return 遥测代号列表
     */
    List<TelemetryCode> getTelemetryCodes(Long id, Long satelliteId);

    /**
     * 查询遥测代号（DTO格式）
     *
     * @param id          遥测代号ID
     * @param satelliteId 航天器ID
     * @return 遥测代号DTO列表
     */
    List<TelemetryCodeDto> getTelemetryCodesDto(Long id, Long satelliteId);





    /**
     * 根据文件名（批次标识）查询未绑定航天器的遥测代号数据
     *
     * @param fileName 文件名（批次标识）
     * @return 遥测代号DTO列表
     */
    List<TelemetryCodeDto> getTelemetryCodesByFileName(String fileName);

    /**
     * 根据文件名（批次标识）删除临时遥测代号数据
     *
     * @param fileName 文件名（批次标识）
     * @return 删除结果
     */
    Map<String, Object> deleteTelemetryCodesByFileName(String fileName);

    /**
     * 根据文件名（批次标识）更新航天器ID关联
     *
     * @param satelliteId 航天器ID
     * @param fileName    文件名（批次标识）
     * @return 更新结果
     */
    Map<String, Object> updateSatelliteIdByFileName(Long satelliteId, String fileName);

    /**
     * 获取航天器已绑定的文件信息
     *
     * @param satelliteId 航天器ID
     * @return 文件信息，包含文件名和数据统计
     */
    Map<String, Object> getBoundFileInfo(Long satelliteId);

    /**
     * 删除航天器已绑定的遥测数据
     *
     * @param satelliteId 航天器ID
     * @return 删除结果
     */
    Map<String, Object> deleteBoundTelemetryData(Long satelliteId);
}