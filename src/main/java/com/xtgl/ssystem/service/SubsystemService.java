package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.dto.SubsystemDto;
import com.xtgl.ssystem.common.entity.Subsystem;

import java.util.List;

/**
 * 分系统服务接口
 */
public interface SubsystemService {
    
    /**
     * 添加分系统
     * @param subsystemDto 分系统信息
     * @return 分系统ID
     */
    Long addSubsystem(SubsystemDto subsystemDto);
    
    /**
     * 查询某航天器下的所有分系统
     * @param satelliteId 航天器ID
     * @return 分系统列表
     */
    List<Subsystem> getSubsystemsBySatelliteId(Long satelliteId);
    
    /**
     * 删除分系统（级联删除其下所有单机及模块）
     * @param subsystemId 分系统ID
     */
    void deleteSubsystem(Long subsystemId);
    
    /**
     * 编辑分系统
     * @param subsystemId 分系统ID
     * @param subsystemDto 分系统信息
     */
    void updateSubsystem(Long subsystemId, SubsystemDto subsystemDto);
    
    /**
     * 根据ID获取分系统
     * @param subsystemId 分系统ID
     * @return 分系统信息
     */
    Subsystem getSubsystemById(Long subsystemId);
}
