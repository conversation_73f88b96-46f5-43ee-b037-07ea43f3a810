package com.xtgl.ssystem.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.util.IpUtil;
import com.xtgl.ssystem.util.MqttClientUtil;
import com.xtgl.ssystem.common.dto.MqttDto;
import com.xtgl.ssystem.common.dto.MqttTestResultDto;
import com.xtgl.ssystem.common.entity.MqttConfig;
import com.xtgl.ssystem.mapper.MqttConfigMapper;
import com.xtgl.ssystem.service.MqttService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * MQTT服务接口实现类
 */
@Service
public class MqttServiceImpl extends ServiceImpl<MqttConfigMapper, MqttConfig> implements MqttService {

    @Autowired
    private MqttConfigMapper mqttConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MqttDto createMqtt(MqttDto mqttDto) {
        // 检查名称是否已存在
        checkNameExists(mqttDto.getName(), null);

        // DTO转换为实体
        MqttConfig mqttConfig = new MqttConfig();
        convertDtoToEntity(mqttDto, mqttConfig);

        // 保存实体
        mqttConfigMapper.insert(mqttConfig);

        // 返回带有ID的DTO
        mqttDto.setId(mqttConfig.getId());

        // 将密码明文设置为null
        mqttDto.setPassword(null);

        return mqttDto;
    }

    @Override
    public MqttTestResultDto testMqttConnection(MqttDto mqttDto) {
        // 测试MQTT连接
        MqttClientUtil.MqttConnectionResult result = MqttClientUtil.testConnection(mqttDto);

        // 转换为DTO
        MqttTestResultDto testResultDto = new MqttTestResultDto();
        BeanUtils.copyProperties(result, testResultDto);

        return testResultDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MqttDto updateMqtt(MqttDto mqttDto) {
        // 检查ID是否存在
        Long id = mqttDto.getId();
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }

        // 检查配置是否存在
        MqttConfig existConfig = mqttConfigMapper.selectById(id);
        if (existConfig == null) {
            throw new IllegalArgumentException("MQTT配置不存在");
        }

        // 检查名称是否已存在（排除自身）
        checkNameExists(mqttDto.getName(), id);

        // 更新实体
        convertDtoToEntity(mqttDto, existConfig);
        mqttConfigMapper.updateById(existConfig);

        // 将密码明文设置为null
        mqttDto.setPassword(null);

        return mqttDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMqtt(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }

        return mqttConfigMapper.deleteById(id) > 0;
    }

    @Override
    public MqttDto getMqttById(Long id) {
        MqttConfig mqttConfig = mqttConfigMapper.selectById(id);
        if (mqttConfig == null) {
            return null;
        }

        MqttDto mqttDto = convertEntityToDto(mqttConfig);
        // 清除密码明文
        mqttDto.setPassword(null);
        return mqttDto;
    }

    @Override
    public MqttDto getMqttByName(String name) {
        if (!StringUtils.hasText(name)) {
            return null;
        }

        LambdaQueryWrapper<MqttConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqttConfig::getName, name);
        MqttConfig mqttConfig = mqttConfigMapper.selectOne(queryWrapper);

        if (mqttConfig == null) {
            return null;
        }

        MqttDto mqttDto = convertEntityToDto(mqttConfig);
        // 清除密码明文
        mqttDto.setPassword(null);
        return mqttDto;
    }

    @Override
    public PageResult<MqttDto> pageMqtt(Integer pageNum, Integer pageSize, Boolean status) {
        // 构建分页参数
        Page<MqttConfig> page = new Page<>(pageNum, pageSize);

        // 查询数据
        IPage<MqttConfig> mqttConfigPage;
        if (status != null) {
            mqttConfigPage = mqttConfigMapper.selectPageByStatus(page, status);
        } else {
            mqttConfigPage = mqttConfigMapper.selectPageByStatus(page, null);
        }

        // 转换为DTO列表
        List<MqttDto> mqttDtoList = new ArrayList<>();
        for (MqttConfig mqttConfig : mqttConfigPage.getRecords()) {
            MqttDto dto = convertEntityToDto(mqttConfig);
            // 清除密码明文
            dto.setPassword(null);
            mqttDtoList.add(dto);
        }

        // 构建分页结果
        return PageResult.build(mqttConfigPage.getTotal(), mqttDtoList);
    }

    @Override
    public List<String> getAllMqttNames() {
        LambdaQueryWrapper<MqttConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqttConfig::getStatus, true); // 只获取启用状态的MQTT连接
        queryWrapper.select(MqttConfig::getName);

        List<MqttConfig> mqttConfigs = mqttConfigMapper.selectList(queryWrapper);

        return mqttConfigs.stream()
                .map(MqttConfig::getName)
                .collect(Collectors.toList());
    }

    /**
     * 检查名称是否已存在
     *
     * @param name 名称
     * @param id   ID（更新时排除自身）
     */
    private void checkNameExists(String name, Long id) {
        LambdaQueryWrapper<MqttConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqttConfig::getName, name);
        if (id != null) {
            queryWrapper.ne(MqttConfig::getId, id);
        }
        Long count = mqttConfigMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("名称已存在");
        }
    }

    /**
     * DTO转换为实体
     *
     * @param mqttDto    MQTT配置DTO
     * @param mqttConfig MQTT配置实体
     */
    private void convertDtoToEntity(MqttDto mqttDto, MqttConfig mqttConfig) {
        BeanUtils.copyProperties(mqttDto, mqttConfig);

        // 处理IP地址转换
        if (StringUtils.hasText(mqttDto.getIp())) {
            mqttConfig.setIp(IpUtil.ipToBytes(mqttDto.getIp()));
        }

        // 处理密码加密（使用SHA-256）
        if (StringUtils.hasText(mqttDto.getPassword())) {
            try {
                MessageDigest digest = MessageDigest.getInstance("SHA-256");
                byte[] hash = digest.digest(mqttDto.getPassword().getBytes(StandardCharsets.UTF_8));
                StringBuilder hexString = new StringBuilder();
                for (byte b : hash) {
                    String hex = Integer.toHexString(0xff & b);
                    if (hex.length() == 1)
                        hexString.append('0');
                    hexString.append(hex);
                }
                mqttConfig.setPassword(hexString.toString());
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException("密码加密失败", e);
            }
        }

        // 设置更新时间
        mqttConfig.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 实体转换为DTO
     *
     * @param mqttConfig MQTT配置实体
     * @return MQTT配置DTO
     */
    private MqttDto convertEntityToDto(MqttConfig mqttConfig) {
        MqttDto mqttDto = new MqttDto();
        BeanUtils.copyProperties(mqttConfig, mqttDto);

        // 处理IP地址转换
        if (mqttConfig.getIp() != null) {
            mqttDto.setIp(IpUtil.bytesToIp(mqttConfig.getIp()));
        }

        return mqttDto;
    }
}