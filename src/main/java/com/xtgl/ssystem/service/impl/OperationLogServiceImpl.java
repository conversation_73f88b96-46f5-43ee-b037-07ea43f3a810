package com.xtgl.ssystem.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xtgl.ssystem.common.dto.OptLogDTO;
import com.xtgl.ssystem.common.dto.OperationLogSearchDTO;
import com.xtgl.ssystem.common.entity.OperationLog;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.mapper.OperationLogMapper;
import com.xtgl.ssystem.service.OperationLogService;
import com.xtgl.ssystem.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作日志服务实现类
 */
@Slf4j
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    public void saveOperationLog(OptLogDTO logDto) {
        try {
            OperationLog operationLog = new OperationLog();
            BeanUtils.copyProperties(logDto, operationLog);
            
            // 处理IP地址转换 - 使用IpUtil工具类
            if (logDto.getIpAddress() != null && !logDto.getIpAddress().isEmpty()) {
                try {
                    String ipAddress = logDto.getIpAddress().trim();
                    log.debug("OperationLogService - 准备转换IP地址: {}", ipAddress);

                    // 检查是否是UUID格式（错误格式）
                    if (ipAddress.length() == 36 && ipAddress.contains("-")) {
                        log.warn("检测到UUID格式的IP地址，使用默认IP: {}", ipAddress);
                        operationLog.setIpAddress(IpUtil.ipToBytes("127.0.0.1"));
                    } else {
                        // 正常的IP地址转换
                        byte[] ipBytes = IpUtil.ipToBytes(ipAddress);
                        log.debug("OperationLogService - IP地址转换成功，字节数组长度: {}", ipBytes.length);
                        operationLog.setIpAddress(ipBytes);
                    }
                } catch (Exception e) {
                    log.warn("IP地址转换失败: {}", logDto.getIpAddress(), e);
                    // 设置默认IP地址 127.0.0.1
                    operationLog.setIpAddress(IpUtil.ipToBytes("127.0.0.1"));
                }
            } else {
                log.debug("OperationLogService - IP地址为空，使用默认IP");
                // 默认IP地址
                operationLog.setIpAddress(IpUtil.ipToBytes("127.0.0.1"));
            }
            
            // 设置创建时间
            operationLog.setCreateTime(LocalDateTime.now());
            
            operationLogMapper.insert(operationLog);
            log.info("操作日志记录成功: {}", logDto.getTitle());
        } catch (Exception e) {
            log.error("操作日志记录失败", e);
        }
    }

    @Override
    public PageResult<OptLogDTO> searchOperationLogs(OperationLogSearchDTO searchDto) {
        Page<OperationLog> page = new Page<>(searchDto.getPageNo(), searchDto.getPageSize());
        
        IPage<OperationLog> result = operationLogMapper.searchOperationLogs(
                page,
                searchDto.getUserName(),
                searchDto.getStartTime(),
                searchDto.getEndTime()
        );
        
        // 转换为DTO
        List<OptLogDTO> dtoList = result.getRecords().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        
        return new PageResult<>(result.getTotal(), dtoList);
    }

    /**
     * 实体转DTO
     */
    private OptLogDTO convertToDto(OperationLog operationLog) {
        OptLogDTO dto = new OptLogDTO();
        BeanUtils.copyProperties(operationLog, dto);
        
        // 处理IP地址转换 - 使用IpUtil工具类
        if (operationLog.getIpAddress() != null) {
            dto.setIpAddress(IpUtil.bytesToIp(operationLog.getIpAddress()));
        } else {
            dto.setIpAddress("未知");
        }
        
        return dto;
    }
}
