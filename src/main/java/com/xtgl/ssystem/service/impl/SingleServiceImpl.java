package com.xtgl.ssystem.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xtgl.ssystem.common.dto.SingleDto;
import com.xtgl.ssystem.common.entity.Single;
import com.xtgl.ssystem.mapper.SingleMapper;
import com.xtgl.ssystem.service.SingleService;
import com.xtgl.ssystem.service.ModuleService;
import com.xtgl.ssystem.service.TelemetryBindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 单机服务实现类
 */
@Slf4j
@Service
public class SingleServiceImpl implements SingleService {

    @Autowired
    private SingleMapper singleMapper;

    @Autowired
    private ModuleService moduleService;

    @Autowired
    private TelemetryBindService telemetryBindService;

    @Override
    @Transactional
    public Long addSingle(SingleDto singleDto) {
        log.info("添加单机: {}", singleDto);
        
        // 创建单机实体
        Single single = new Single();
        BeanUtils.copyProperties(singleDto, single);
        
        // 保存到数据库
        int result = singleMapper.insert(single);
        if (result > 0) {
            log.info("单机添加成功，ID: {}", single.getId());
            return single.getId();
        } else {
            throw new RuntimeException("单机添加失败");
        }
    }

    @Override
    public List<Single> getSinglesBySubsystemId(Long subsystemId) {
        log.info("查询分系统{}下的单机", subsystemId);
        
        LambdaQueryWrapper<Single> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Single::getSubsystemId, subsystemId)
                   .orderByAsc(Single::getId);
        
        List<Single> singles = singleMapper.selectList(queryWrapper);
        log.info("查询到{}个单机", singles.size());
        return singles;
    }

    @Override
    @Transactional
    public void deleteSingle(Long singleId) {
        log.info("删除单机: {}", singleId);

        // 检查单机是否存在
        Single single = singleMapper.selectById(singleId);
        if (single == null) {
            throw new RuntimeException("单机不存在");
        }

        // 级联删除：先删除该单机下的所有模块（会自动删除模块的遥测代号绑定）
        moduleService.deleteModulesBySingleId(singleId);

        // 删除单机绑定的遥测代号
        int telemetryDeleted = telemetryBindService.unbindTelemetryCode("single", singleId);
        log.info("删除了{}条单机遥测代号绑定", telemetryDeleted);

        // 删除单机
        int result = singleMapper.deleteById(singleId);
        if (result > 0) {
            log.info("单机删除成功");
        } else {
            throw new RuntimeException("单机删除失败");
        }
    }

    @Override
    @Transactional
    public void updateSingle(Long singleId, SingleDto singleDto) {
        log.info("编辑单机: ID={}, data={}", singleId, singleDto);
        
        // 检查单机是否存在
        Single existingSingle = singleMapper.selectById(singleId);
        if (existingSingle == null) {
            throw new RuntimeException("单机不存在");
        }
        
        // 更新单机信息
        Single single = new Single();
        BeanUtils.copyProperties(singleDto, single);
        single.setId(singleId);
        
        int result = singleMapper.updateById(single);
        if (result > 0) {
            log.info("单机编辑成功");
        } else {
            throw new RuntimeException("单机编辑失败");
        }
    }

    @Override
    public Single getSingleById(Long singleId) {
        log.info("查询单机: {}", singleId);
        
        Single single = singleMapper.selectById(singleId);
        if (single == null) {
            throw new RuntimeException("单机不存在");
        }
        
        return single;
    }

    @Override
    @Transactional
    public void deleteSinglesBySubsystemId(Long subsystemId) {
        log.info("删除分系统{}下的所有单机", subsystemId);
        
        // 查询该分系统下的所有单机
        List<Single> singles = getSinglesBySubsystemId(subsystemId);
        
        // 逐个删除单机（会级联删除模块）
        for (Single single : singles) {
            deleteSingle(single.getId());
        }
        
        log.info("分系统{}下的所有单机删除完成", subsystemId);
    }
}
