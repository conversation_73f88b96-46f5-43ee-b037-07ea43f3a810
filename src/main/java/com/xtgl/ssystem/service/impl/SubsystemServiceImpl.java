package com.xtgl.ssystem.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xtgl.ssystem.common.dto.SubsystemDto;
import com.xtgl.ssystem.common.entity.Subsystem;
import com.xtgl.ssystem.mapper.SubsystemMapper;
import com.xtgl.ssystem.service.SubsystemService;
import com.xtgl.ssystem.service.SingleService;
import com.xtgl.ssystem.service.TelemetryBindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 分系统服务实现类
 */
@Slf4j
@Service
public class SubsystemServiceImpl implements SubsystemService {

    @Autowired
    private SubsystemMapper subsystemMapper;

    @Autowired
    private SingleService singleService;

    @Autowired
    private TelemetryBindService telemetryBindService;

    @Override
    @Transactional
    public Long addSubsystem(SubsystemDto subsystemDto) {
        log.info("添加分系统: {}", subsystemDto);
        
        // 创建分系统实体
        Subsystem subsystem = new Subsystem();
        BeanUtils.copyProperties(subsystemDto, subsystem);
        
        // 保存到数据库
        int result = subsystemMapper.insert(subsystem);
        if (result > 0) {
            log.info("分系统添加成功，ID: {}", subsystem.getId());
            return subsystem.getId();
        } else {
            throw new RuntimeException("分系统添加失败");
        }
    }

    @Override
    public List<Subsystem> getSubsystemsBySatelliteId(Long satelliteId) {
        log.info("查询航天器{}下的分系统", satelliteId);

        LambdaQueryWrapper<Subsystem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Subsystem::getSpacecraftId, satelliteId)
                   .orderByAsc(Subsystem::getId);

        List<Subsystem> subsystems = subsystemMapper.selectList(queryWrapper);
        log.info("查询到{}个分系统", subsystems.size());
        return subsystems;
    }

    @Override
    @Transactional
    public void deleteSubsystem(Long subsystemId) {
        log.info("删除分系统: {}", subsystemId);

        // 检查分系统是否存在
        Subsystem subsystem = subsystemMapper.selectById(subsystemId);
        if (subsystem == null) {
            throw new RuntimeException("分系统不存在");
        }

        // 级联删除：先删除该分系统下的所有单机（会自动级联删除模块和遥测代号绑定）
        singleService.deleteSinglesBySubsystemId(subsystemId);

        // 删除分系统绑定的遥测代号
        int telemetryDeleted = telemetryBindService.unbindTelemetryCode("subsystem", subsystemId);
        log.info("删除了{}条分系统遥测代号绑定", telemetryDeleted);

        // 删除分系统
        int result = subsystemMapper.deleteById(subsystemId);
        if (result > 0) {
            log.info("分系统删除成功");
        } else {
            throw new RuntimeException("分系统删除失败");
        }
    }

    @Override
    @Transactional
    public void updateSubsystem(Long subsystemId, SubsystemDto subsystemDto) {
        log.info("编辑分系统: ID={}, data={}", subsystemId, subsystemDto);
        
        // 检查分系统是否存在
        Subsystem existingSubsystem = subsystemMapper.selectById(subsystemId);
        if (existingSubsystem == null) {
            throw new RuntimeException("分系统不存在");
        }
        
        // 更新分系统信息
        Subsystem subsystem = new Subsystem();
        BeanUtils.copyProperties(subsystemDto, subsystem);
        subsystem.setId(subsystemId);
        
        int result = subsystemMapper.updateById(subsystem);
        if (result > 0) {
            log.info("分系统编辑成功");
        } else {
            throw new RuntimeException("分系统编辑失败");
        }
    }

    @Override
    public Subsystem getSubsystemById(Long subsystemId) {
        log.info("查询分系统: {}", subsystemId);
        
        Subsystem subsystem = subsystemMapper.selectById(subsystemId);
        if (subsystem == null) {
            throw new RuntimeException("分系统不存在");
        }
        
        return subsystem;
    }
}
