package com.xtgl.ssystem.service.impl;

import com.xtgl.ssystem.common.dto.TelemetryBindDto;
import com.xtgl.ssystem.common.dto.TelemetryBindResponseDto;
import com.xtgl.ssystem.common.entity.TelemetryCode;
import com.xtgl.ssystem.common.entity.Satellite;
import com.xtgl.ssystem.common.entity.Subsystem;
import com.xtgl.ssystem.common.entity.Single;
import com.xtgl.ssystem.common.entity.Module;
import com.xtgl.ssystem.mapper.TelemetryCodeMapper;
import com.xtgl.ssystem.mapper.SatelliteMapper;
import com.xtgl.ssystem.mapper.SubsystemMapper;
import com.xtgl.ssystem.mapper.SingleMapper;
import com.xtgl.ssystem.mapper.ModuleMapper;
import com.xtgl.ssystem.service.TelemetryBindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 遥测代号绑定服务实现
 */
@Slf4j
@Service
public class TelemetryBindServiceImpl implements TelemetryBindService {

    @Autowired
    private TelemetryCodeMapper telemetryCodeMapper;

    @Autowired
    private SatelliteMapper satelliteMapper;

    @Autowired
    private SubsystemMapper subsystemMapper;

    @Autowired
    private SingleMapper singleMapper;

    @Autowired
    private ModuleMapper moduleMapper;

    @Override
    @Transactional
    public TelemetryBindResponseDto bindTelemetryCode(TelemetryBindDto bindDto) {
        log.info("绑定遥测代号: level={}, id={}, telemetryCodeId={}",
                bindDto.getLevel(), bindDto.getId(), bindDto.getTelemetryCodeId());

        // 1. 验证层级和ID的有效性
        validateLevelAndId(bindDto.getLevel(), bindDto.getId());

        // 2. 验证遥测代号是否存在
        TelemetryCode telemetryCode = telemetryCodeMapper.selectById(bindDto.getTelemetryCodeId());
        if (telemetryCode == null) {
            throw new RuntimeException("遥测代号不存在: " + bindDto.getTelemetryCodeId());
        }

        // 3. 验证层级继承规则
        validateHierarchyRule(bindDto.getLevel(), bindDto.getId(), telemetryCode.getName());

        // 4. 检查该对象是否已经绑定同一遥测代号（按名称检查，因为允许多个对象绑定同一遥测代号）
        List<TelemetryCode> existingCodes = getBoundTelemetryCodes(bindDto.getLevel(), bindDto.getId());
        boolean alreadyBound = existingCodes.stream()
                .anyMatch(code -> code.getName().equals(telemetryCode.getName()));
        if (alreadyBound) {
            throw new RuntimeException("该对象已绑定遥测代号: " + telemetryCode.getName());
        }

        // 5. 创建新的绑定记录（复制原遥测代号信息）
        TelemetryCode newBinding = new TelemetryCode();
        newBinding.setSerialNum(telemetryCode.getSerialNum());
        newBinding.setName(telemetryCode.getName());
        newBinding.setDescription(telemetryCode.getDescription());
        newBinding.setNote(telemetryCode.getNote());
        newBinding.setFileName(telemetryCode.getFileName());

        // 设置航天器ID（所有绑定都属于同一个航天器）
        newBinding.setSpacecraftId(getSpacecraftId(bindDto.getLevel(), bindDto.getId()));

        // 设置对应层级的ID
        switch (bindDto.getLevel()) {
            case "subsystem":
                newBinding.setSubsystemId(bindDto.getId());
                break;
            case "single":
                newBinding.setSingleId(bindDto.getId());
                // 同时设置分系统ID（保持层级关系）
                newBinding.setSubsystemId(getSubsystemIdForSingle(bindDto.getId()));
                break;
            case "module":
                newBinding.setModuleId(bindDto.getId());
                // 同时设置单机ID和分系统ID（保持层级关系）
                Long singleId = getSingleIdForModule(bindDto.getId());
                newBinding.setSingleId(singleId);
                newBinding.setSubsystemId(getSubsystemIdForSingle(singleId));
                break;
        }

        int result = telemetryCodeMapper.insert(newBinding);
        if (result <= 0) {
            throw new RuntimeException("绑定遥测代号失败");
        }

        // 6. 构建响应
        TelemetryBindResponseDto response = new TelemetryBindResponseDto();
        response.setLevel(bindDto.getLevel());
        response.setId(bindDto.getId());
        response.setTelemetryCode(telemetryCode.getName());
        response.setBoundAt(LocalDateTime.now());
        response.setTelemetryCodeId(newBinding.getId());

        log.info("遥测代号绑定成功");
        return response;
    }

    @Override
    public List<TelemetryCode> getBoundTelemetryCodes(String level, Long id) {
        List<TelemetryCode> boundCodes;

        switch (level) {
            case "satellite":
                boundCodes = telemetryCodeMapper.selectBySatelliteId(id);
                break;
            case "subsystem":
                boundCodes = telemetryCodeMapper.selectBySubsystemId(id);
                break;
            case "single":
                boundCodes = telemetryCodeMapper.selectBySingleId(id);
                break;
            case "module":
                boundCodes = telemetryCodeMapper.selectByModuleId(id);
                break;
            default:
                throw new RuntimeException("不支持的层级类型: " + level);
        }

        // 所有层级都需要去重，因为可能有多个下级对象绑定了相同的遥测代号
        return deduplicateTelemetryCodes(boundCodes);
    }

    @Override
    public List<TelemetryCode> getAvailableTelemetryCodes(String level, Long id) {
        List<TelemetryCode> availableCodes;

        // 根据层级继承规则，返回父级已绑定的遥测代号
        switch (level) {
            case "satellite":
                // 航天器层级可以绑定所有遥测代号（这里返回该航天器的所有遥测代号）
                availableCodes = telemetryCodeMapper.selectBySatelliteId(id);
                break;
            case "subsystem":
                // 分系统只能绑定其所属航天器已绑定的遥测代号
                Long spacecraftId = getSpacecraftIdForSubsystem(id);
                availableCodes = telemetryCodeMapper.selectBySatelliteId(spacecraftId);
                break;
            case "single":
                // 单机只能绑定其所属分系统已绑定的遥测代号
                Long subsystemId = getSubsystemIdForSingle(id);
                availableCodes = telemetryCodeMapper.selectBySubsystemId(subsystemId);
                break;
            case "module":
                // 模块只能绑定其所属单机已绑定的遥测代号
                Long singleId = getSingleIdForModule(id);
                availableCodes = telemetryCodeMapper.selectBySingleId(singleId);
                break;
            default:
                throw new RuntimeException("不支持的层级类型: " + level);
        }

        // 去重：按遥测代号名称去重，只保留一个代表性记录
        return deduplicateTelemetryCodes(availableCodes);
    }

    @Override
    @Transactional
    public int unbindTelemetryCode(String level, Long id) {
        log.info("删除遥测代号绑定: level={}, id={}", level, id);
        return telemetryCodeMapper.deleteByLevelAndId(level, id);
    }

    /**
     * 删除特定的遥测代号绑定记录
     */
    @Transactional
    public int unbindSpecificTelemetryCode(Long telemetryCodeId) {
        log.info("删除特定遥测代号绑定: telemetryCodeId={}", telemetryCodeId);
        return telemetryCodeMapper.deleteById(telemetryCodeId);
    }

    @Override
    @Transactional
    public int unbindTelemetryCodeBySerialNum(String level, Long levelId, Integer serialNum) {
        log.info("根据层级和序号删除遥测代号绑定: level={}, levelId={}, serialNum={}", level, levelId, serialNum);
        return telemetryCodeMapper.deleteByLevelAndSerialNum(level, levelId, serialNum);
    }

    /**
     * 验证层级和ID的有效性
     */
    private void validateLevelAndId(String level, Long id) {
        switch (level) {
            case "satellite":
                if (satelliteMapper.selectById(id) == null) {
                    throw new RuntimeException("航天器不存在");
                }
                break;
            case "subsystem":
                if (subsystemMapper.selectById(id) == null) {
                    throw new RuntimeException("分系统不存在");
                }
                break;
            case "single":
                if (singleMapper.selectById(id) == null) {
                    throw new RuntimeException("单机不存在");
                }
                break;
            case "module":
                if (moduleMapper.selectById(id) == null) {
                    throw new RuntimeException("模块不存在");
                }
                break;
            default:
                throw new RuntimeException("不支持的层级类型: " + level);
        }
    }

    /**
     * 验证层级继承规则
     */
    private void validateHierarchyRule(String level, Long id, String telemetryCode) {
        switch (level) {
            case "satellite":
                // 航天器层级无需验证父级
                break;
            case "subsystem":
                // 验证所属航天器是否已绑定该遥测代号
                Long spacecraftId = getSpacecraftIdForSubsystem(id);
                if (!isCodeBoundToLevel("satellite", spacecraftId, telemetryCode)) {
                    throw new RuntimeException("父级未绑定遥测代号 " + telemetryCode);
                }
                break;
            case "single":
                // 验证所属分系统是否已绑定该遥测代号
                Long subsystemId = getSubsystemIdForSingle(id);
                if (!isCodeBoundToLevel("subsystem", subsystemId, telemetryCode)) {
                    throw new RuntimeException("父级未绑定遥测代号 " + telemetryCode);
                }
                break;
            case "module":
                // 验证所属单机是否已绑定该遥测代号
                Long singleId = getSingleIdForModule(id);
                if (!isCodeBoundToLevel("single", singleId, telemetryCode)) {
                    throw new RuntimeException("父级未绑定遥测代号 " + telemetryCode);
                }
                break;
        }
    }

    /**
     * 检查指定层级是否已绑定指定遥测代号
     */
    private boolean isCodeBoundToLevel(String level, Long id, String telemetryCode) {
        List<TelemetryCode> boundCodes = getBoundTelemetryCodes(level, id);
        return boundCodes.stream().anyMatch(code -> code.getName().equals(telemetryCode));
    }

    /**
     * 获取航天器ID
     */
    private Long getSpacecraftId(String level, Long id) {
        switch (level) {
            case "satellite":
                return id;
            case "subsystem":
                return getSpacecraftIdForSubsystem(id);
            case "single":
                Single single = singleMapper.selectById(id);
                return single.getSpacecraftId();
            case "module":
                Module module = moduleMapper.selectById(id);
                return module.getSpacecraftId();
            default:
                throw new RuntimeException("不支持的层级类型: " + level);
        }
    }

    private Long getSpacecraftIdForSubsystem(Long subsystemId) {
        Subsystem subsystem = subsystemMapper.selectById(subsystemId);
        return subsystem.getSpacecraftId();
    }

    private Long getSubsystemIdForSingle(Long singleId) {
        Single single = singleMapper.selectById(singleId);
        return single.getSubsystemId();
    }

    private Long getSingleIdForModule(Long moduleId) {
        Module module = moduleMapper.selectById(moduleId);
        return module.getSingleId();
    }

    /**
     * 去重遥测代号列表，按名称去重，保留第一个出现的记录
     */
    private List<TelemetryCode> deduplicateTelemetryCodes(List<TelemetryCode> telemetryCodes) {
        if (telemetryCodes == null || telemetryCodes.isEmpty()) {
            return telemetryCodes;
        }

        // 按遥测代号名称去重，保留第一个出现的记录
        Map<String, TelemetryCode> uniqueCodes = telemetryCodes.stream()
                .collect(Collectors.toMap(
                    TelemetryCode::getName,
                    code -> code,
                    (existing, replacement) -> existing // 保留第一个出现的记录
                ));

        return uniqueCodes.values().stream()
                .sorted((a, b) -> Integer.compare(a.getSerialNum(), b.getSerialNum()))
                .collect(Collectors.toList());
    }
}
