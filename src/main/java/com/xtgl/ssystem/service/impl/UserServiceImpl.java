package com.xtgl.ssystem.service.impl;

import com.xtgl.ssystem.common.dto.LoginDto;
import com.xtgl.ssystem.common.dto.LoginResponseDto;
import com.xtgl.ssystem.common.entity.User;
import com.xtgl.ssystem.mapper.UserMapper;
import com.xtgl.ssystem.service.UserService;
import com.xtgl.ssystem.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public LoginResponseDto login(LoginDto loginDto) {
        log.info("用户登录请求，账号：{}", loginDto.getAccount());
        
        // 1. 根据账号查找用户
        User user = userMapper.findByAccount(loginDto.getAccount());
        if (user == null) {
            log.warn("用户不存在，账号：{}", loginDto.getAccount());
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 2. 验证密码（使用bcrypt）
        if (!passwordEncoder.matches(loginDto.getPassword(), user.getPassword())) {
            log.warn("密码错误，账号：{}", loginDto.getAccount());
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 3. 生成JWT令牌
        String token = jwtUtil.generateToken(user.getId(), user.getAccount());
        
        log.info("用户登录成功，账号：{}，用户ID：{}", user.getAccount(), user.getId());
        
        // 4. 返回登录响应
        return new LoginResponseDto(token, user.getAccount(), user.getId());
    }
} 