package com.xtgl.ssystem.util;

import com.xtgl.ssystem.common.dto.SatelliteDto;
import com.xtgl.ssystem.common.entity.TelemetryCode;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Excel处理工具类
 */
public class ExcelUtil {

    // Excel列名与TelemetryCode属性的映射
    private static final Map<String, String> TELEMETRY_COLUMN_MAPPING = new HashMap<>();
    
    // 临时存储解析后的遥测代号数据
    private static final Map<String, List<TelemetryCode>> TEMP_TELEMETRY_CODE_CACHE = new ConcurrentHashMap<>();
    
    static {
        TELEMETRY_COLUMN_MAPPING.put("序号", "serialNum");
        TELEMETRY_COLUMN_MAPPING.put("代号名称", "name");
        TELEMETRY_COLUMN_MAPPING.put("代号描述", "description");
        TELEMETRY_COLUMN_MAPPING.put("备注", "note");
        TELEMETRY_COLUMN_MAPPING.put("分系统ID", "subsystemId");
        TELEMETRY_COLUMN_MAPPING.put("单机ID", "singleId");
        TELEMETRY_COLUMN_MAPPING.put("模块ID", "moduleId");
        TELEMETRY_COLUMN_MAPPING.put("文件名称", "fileName");
    }

    // 导出临时目录
    private static final String EXPORT_TEMP_DIR = "export";
    
    // 下载目录
    private static final String DOWNLOAD_DIR = "download";

    /**
     * 保存解析结果到临时存储
     *
     * @param uploadId      上传ID
     * @param telemetryCodes 遥测代号数据列表
     */
    public static void saveToTempStorage(String uploadId, List<TelemetryCode> telemetryCodes) {
        TEMP_TELEMETRY_CODE_CACHE.put(uploadId, new ArrayList<>(telemetryCodes));
    }

    /**
     * 从临时存储获取遥测代号数据
     *
     * @param uploadId 上传ID
     * @return 遥测代号数据列表，如果不存在则返回空列表
     */
    public static List<TelemetryCode> getFromTempStorage(String uploadId) {
        return TEMP_TELEMETRY_CODE_CACHE.getOrDefault(uploadId, Collections.emptyList());
    }

    /**
     * 从临时存储移除遥测代号数据
     *
     * @param uploadId 上传ID
     * @return 被移除的数据，如果不存在则返回空列表
     */
    public static List<TelemetryCode> removeFromTempStorage(String uploadId) {
        List<TelemetryCode> removed = TEMP_TELEMETRY_CODE_CACHE.remove(uploadId);
        return removed != null ? removed : Collections.emptyList();
    }



    /**
     * 解析遥测代号Excel
     *
     * @param file        Excel文件
     * @param satelliteId 航天器ID
     * @return 解析结果
     * @throws IOException IO异常
     */
    public static Map<String, Object> parseTelemetryCodeExcel(MultipartFile file, Long satelliteId) throws IOException {
        // 结果容器
        List<TelemetryCode> successList = new ArrayList<>();
        List<Map<String, Object>> failList = new ArrayList<>();
        String errorMsg = null;

        try (InputStream is = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(is)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 检查文件是否为空
            if (sheet.getPhysicalNumberOfRows() == 0) {
                throw new IllegalArgumentException("Excel文件内容为空");
            }

            // 获取表头行
            Row headerRow = sheet.getRow(0);

            // 验证表头
            Map<Integer, String> columnMapping = validateHeader(headerRow);
            if (columnMapping == null) {
                throw new IllegalArgumentException("表头格式不正确，请确保包含必要的列：序号、代号名称、代号描述等");
            }

            // 限制最大行数
            int maxRows = Math.min(sheet.getLastRowNum(), 5000);

            // 逐行解析数据
            for (int i = 1; i <= maxRows; i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                try {
                    // 解析单行数据
                    TelemetryCode telemetryCode = parseTelemetryCodeRow(row, columnMapping, satelliteId);
                    successList.add(telemetryCode);
                } catch (Exception e) {
                    // 记录失败行
                    Map<String, Object> failRow = new HashMap<>();
                    failRow.put("rowNum", i + 1);
                    failRow.put("error", e.getMessage());

                    // 保存原始数据
                    for (Map.Entry<Integer, String> entry : columnMapping.entrySet()) {
                        Cell cell = row.getCell(entry.getKey());
                        failRow.put(entry.getValue(), getCellValueAsString(cell));
                    }

                    failList.add(failRow);
                }
            }
        } catch (IllegalArgumentException e) {
            errorMsg = e.getMessage();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("successList", successList);
        result.put("failList", failList);
        result.put("errorMsg", errorMsg);

        return result;
    }

    /**
     * 验证表头格式
     *
     * @param headerRow 表头行
     * @return 列索引与属性名映射，验证失败返回null
     */
    private static Map<Integer, String> validateHeader(Row headerRow) {
        if (headerRow == null) {
            return null;
        }
        
        Map<Integer, String> columnMapping = new HashMap<>();
        int requiredColumnsFound = 0;
        
        // 必需的列
        Set<String> requiredColumns = new HashSet<>(Arrays.asList("序号", "代号名称"));
        
        // 遍历表头单元格
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell == null) continue;
            
            String cellValue = cell.getStringCellValue().trim();
            String propertyName = TELEMETRY_COLUMN_MAPPING.get(cellValue);
            
            // 存在于映射中的列才记录
            if (propertyName != null) {
                columnMapping.put(i, propertyName);
                
                // 统计必需列的数量
                if (requiredColumns.contains(cellValue)) {
                    requiredColumnsFound++;
                }
            }
        }
        
        // 检查是否包含所有必需的列
        return requiredColumnsFound == requiredColumns.size() ? columnMapping : null;
    }

    /**
     * 解析单行遥测代号数据
     *
     * @param row           数据行
     * @param columnMapping 列映射
     * @param satelliteId   航天器ID

     * @return 遥测代号对象
     */
    private static TelemetryCode parseTelemetryCodeRow(Row row, Map<Integer, String> columnMapping, Long satelliteId) {
        TelemetryCode telemetryCode = new TelemetryCode();
        
        // 设置航天器ID
        telemetryCode.setSpacecraftId(satelliteId);
        

        
        // 遍历映射的列
        for (Map.Entry<Integer, String> entry : columnMapping.entrySet()) {
            int columnIndex = entry.getKey();
            String propertyName = entry.getValue();
            Cell cell = row.getCell(columnIndex);
            
            // 根据属性名设置对应的值
            switch (propertyName) {
                case "serialNum":
                    telemetryCode.setSerialNum(getIntCellValue(cell));
                    break;
                case "name":
                    String name = getStringCellValue(cell);
                    if (name == null || name.trim().isEmpty()) {
                        throw new IllegalArgumentException("代号名称不能为空");
                    }
                    telemetryCode.setName(name);
                    break;
                case "description":
                    telemetryCode.setDescription(getStringCellValue(cell));
                    break;
                case "note":
                    telemetryCode.setNote(getStringCellValue(cell));
                    break;
                case "subsystemId":
                    telemetryCode.setSubsystemId(getLongCellValue(cell));
                    break;
                case "singleId":
                    telemetryCode.setSingleId(getLongCellValue(cell));
                    break;
                case "moduleId":
                    telemetryCode.setModuleId(getLongCellValue(cell));
                    break;
                case "fileName":
                    telemetryCode.setFileName(getStringCellValue(cell));
                    break;
            }
        }
        
        return telemetryCode;
    }



    /**
     * 获取单元格字符串值
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private static String getStringCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }

    /**
     * 获取单元格整数值
     *
     * @param cell 单元格
     * @return 整数值
     */
    private static Integer getIntCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return (int) cell.getNumericCellValue();
            case STRING:
                try {
                    return Integer.parseInt(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }

    /**
     * 获取单元格Long值
     *
     * @param cell 单元格
     * @return Long值
     */
    private static Long getLongCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return (long) cell.getNumericCellValue();
            case STRING:
                try {
                    return Long.parseLong(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }

    /**
     * 获取单元格值的字符串表示
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                }
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case ERROR:
                return "ERROR: " + cell.getErrorCellValue();
            default:
                return "";
        }
    }

    /**
     * 生成失败数据Excel文件
     *
     * @param failList 失败数据列表
     * @return 文件路径
     * @throws IOException IO异常
     */
    public static String generateFailExcel(List<Map<String, Object>> failList) throws IOException {
        // 创建下载目录
        Path downloadPath = Paths.get(DOWNLOAD_DIR);
        if (!Files.exists(downloadPath)) {
            Files.createDirectories(downloadPath);
        }
        
        // 生成文件名
        String fileName = "fail_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")) + ".xlsx";
        String filePath = DOWNLOAD_DIR + File.separator + fileName;
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("失败数据");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            
            // 添加基本列
            int columnIndex = 0;
            headerRow.createCell(columnIndex++).setCellValue("行号");
            headerRow.createCell(columnIndex++).setCellValue("错误信息");
            
            // 添加数据列
            if (!failList.isEmpty()) {
                Map<String, Object> firstFailRow = failList.get(0);
                for (String key : firstFailRow.keySet()) {
                    if (!key.equals("rowNum") && !key.equals("error")) {
                        headerRow.createCell(columnIndex++).setCellValue(key);
                    }
                }
            }
            
            // 填充数据
            int rowNum = 1;
            for (Map<String, Object> failRow : failList) {
                Row row = sheet.createRow(rowNum++);
                
                // 添加行号和错误信息
                columnIndex = 0;
                row.createCell(columnIndex++).setCellValue(failRow.get("rowNum").toString());
                row.createCell(columnIndex++).setCellValue(failRow.get("error").toString());
                
                // 添加数据列
                for (String key : failRow.keySet()) {
                    if (!key.equals("rowNum") && !key.equals("error")) {
                        Object value = failRow.get(key);
                        row.createCell(columnIndex++).setCellValue(value != null ? value.toString() : "");
                    }
                }
            }
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
        }
        
        return "/" + filePath;
    }

    /**
     * 导出航天器列表为Excel
     *
     * @param list 航天器列表
     * @return 文件路径
     * @throws IOException IO异常
     */
    public static String exportSatellitesToExcel(List<SatelliteDto> list) throws IOException {
        // 创建导出临时目录
        Path exportPath = Paths.get(EXPORT_TEMP_DIR);
        if (!Files.exists(exportPath)) {
            Files.createDirectories(exportPath);
        }
        
        // 生成文件名
        String fileName = "satellites_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")) + ".xlsx";
        String filePath = EXPORT_TEMP_DIR + File.separator + fileName;
        
        // 使用SXSSF提高大数据量写入性能
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) { // 窗口大小100
            Sheet sheet = workbook.createSheet("航天器列表");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            int columnIndex = 0;
            
            // 添加所有列（包括隐藏列）
            headerRow.createCell(columnIndex++).setCellValue("ID");
            headerRow.createCell(columnIndex++).setCellValue("型号");
            headerRow.createCell(columnIndex++).setCellValue("名称");
            headerRow.createCell(columnIndex++).setCellValue("负责人");
            headerRow.createCell(columnIndex++).setCellValue("创建时间");
            headerRow.createCell(columnIndex++).setCellValue("所属单位");
            headerRow.createCell(columnIndex++).setCellValue("开始接收时间");
            headerRow.createCell(columnIndex++).setCellValue("卫星状态");
            headerRow.createCell(columnIndex++).setCellValue("批次");
            headerRow.createCell(columnIndex++).setCellValue("MQTT名称");

            
            // 填充数据
            int rowNum = 1;
            for (SatelliteDto dto : list) {
                Row row = sheet.createRow(rowNum++);
                columnIndex = 0;
                
                row.createCell(columnIndex++).setCellValue(dto.getId() != null ? dto.getId() : 0);
                row.createCell(columnIndex++).setCellValue(dto.getModel() != null ? dto.getModel() : "");
                row.createCell(columnIndex++).setCellValue(dto.getName() != null ? dto.getName() : "");
                row.createCell(columnIndex++).setCellValue(dto.getHeader() != null ? dto.getHeader() : "");
                row.createCell(columnIndex++).setCellValue(dto.getCreateTime() != null ? dto.getCreateTime().toString() : "");
                row.createCell(columnIndex++).setCellValue(dto.getCompany() != null ? dto.getCompany() : "");
                row.createCell(columnIndex++).setCellValue(dto.getReceiveTime() != null ? dto.getReceiveTime().toString() : "");
                row.createCell(columnIndex++).setCellValue(dto.getStatus() != null ? dto.getStatus() : "");
                row.createCell(columnIndex++).setCellValue(dto.getBatch() != null ? dto.getBatch() : 0);
                row.createCell(columnIndex++).setCellValue(dto.getMqttName() != null ? dto.getMqttName() : "");
            }
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
            
            // 清除临时文件
            workbook.dispose();
        }
        
        return filePath;
    }
} 