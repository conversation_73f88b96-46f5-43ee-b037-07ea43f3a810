package com.xtgl.ssystem.util;

import com.xtgl.ssystem.common.dto.MqttDto;
import lombok.Data;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.util.UUID;

/**
 * MQTT客户端工具类
 */
public class MqttClientUtil {

    /**
     * 测试MQTT连接
     *
     * @param mqttDto MQTT配置
     * @return 连接结果
     */
    public static MqttConnectionResult testConnection(MqttDto mqttDto) {
        MqttConnectionResult result = new MqttConnectionResult();
        MqttClient client = null;
        long startTime = System.currentTimeMillis();

        try {
            // 构建服务器URI
            String serverURI = String.format("tcp://%s:%d", mqttDto.getIp(), mqttDto.getPort());
            String clientId = "mqtt_test_" + UUID.randomUUID().toString().substring(0, 8);

            // 创建MQTT客户端
            client = new MqttClient(serverURI, clientId, new MemoryPersistence());
            MqttConnectOptions options = new MqttConnectOptions();
            
            // 设置用户名，如果为null则不设置
            if (mqttDto.getUserName() != null) {
                options.setUserName(mqttDto.getUserName());
            }
            
            // 设置密码，如果为null则使用空密码
            if (mqttDto.getPassword() != null) {
                options.setPassword(mqttDto.getPassword().toCharArray());
            } else {
                options.setPassword(new char[0]); // 设置空密码
            }
            
            options.setConnectionTimeout(5); // 5秒连接超时
            options.setAutomaticReconnect(false);
            options.setCleanSession(true);

            // 连接到MQTT服务器
            client.connect(options);

            // 记录连接结果
            result.setConnected(true);
            result.setTimeCostMs(System.currentTimeMillis() - startTime);
            result.setMqttVersion(getMqttVersion(client));

        } catch (MqttException e) {
            result.setConnected(false);
            result.setErrorMsg(e.getMessage());
            result.setTimeCostMs(System.currentTimeMillis() - startTime);
        } finally {
            // 断开连接
            try {
                if (client != null && client.isConnected()) {
                    client.disconnect();
                    client.close();
                }
            } catch (MqttException e) {
                // 忽略关闭异常
            }
        }
        return result;
    }

    /**
     * 获取MQTT协议版本
     *
     * @param client MQTT客户端
     * @return MQTT协议版本
     */
    private static String getMqttVersion(MqttClient client) {
        try {
            if (client != null && client.isConnected()) {
                // 根据连接属性推断版本
                if (client.getServerURI().startsWith("ws")) {
                    return "3.1.1 (WebSocket)";
                } else {
                    return "3.1.1";
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "未知";
    }

    /**
     * MQTT连接结果类
     */
    @Data
    public static class MqttConnectionResult {
        private boolean connected;
        private long timeCostMs;
        private String mqttVersion;
        private String errorMsg;
    }
} 