<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xtgl.ssystem.mapper.MqttConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xtgl.ssystem.common.entity.MqttConfig">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="ip" property="ip" />
        <result column="port" property="port" />
        <result column="topic" property="topic" />
        <result column="user_name" property="userName" />
        <result column="password" property="password" />
        <result column="note" property="note" />
        <result column="status" property="status" />
        <result column="update_person" property="updatePerson" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, ip, port, topic, user_name, password, note, status, update_person, update_time
    </sql>

    <!-- 分页查询MQTT配置 -->
    <select id="selectPageByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM mqtt_table
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY update_time DESC
    </select>

</mapper> 