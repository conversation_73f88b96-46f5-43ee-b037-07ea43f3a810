<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xtgl.ssystem.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.xtgl.ssystem.common.entity.User">
        <id column="id" property="id" />
        <result column="account" property="account" />
        <result column="password" property="password" />
    </resultMap>

    <sql id="Base_Column_List">
        id, account, password
    </sql>

</mapper> 