import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例 
const service = axios.create({
  baseURL: '', // 不需要前缀，因为我们直接通过Vite代理到后端
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 调试日志
    console.log(`[请求] ${config.method.toUpperCase()} ${config.url}`, config);
    
    // 添加token认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 调试日志
    console.log(`[响应] ${response.config.url}`, response.data);
    
    if (response.config.url.includes('/mqtt') && !response.config.url.includes('/mqtt/test')) {
      console.log('MQTT请求详细数据:', {
        请求URL: response.config.url,
        请求方法: response.config.method,
        请求参数: response.config.params,
        响应状态: response.status,
        响应数据: response.data
      });

      // 特殊处理MQTT列表接口
      if (response.config.params && response.config.params.pageNum) {
        console.log('MQTT分页数据结构检查:', {
          'data存在?': !!response.data.data,
          'list存在?': !!(response.data.data && response.data.data.list),
          'total存在?': !!(response.data.data && response.data.data.total !== undefined),
          'total值': response.data.data && response.data.data.total,
          '列表长度': response.data.data && response.data.data.list ? response.data.data.list.length : 0
        });
      }
    }
    
    const res = response.data
    
    // 兼容不同的响应格式
    if (res.success !== undefined) {
      // 新的登录接口使用 success 字段
      if (!res.success) {
        ElMessage({
          message: res.message || '请求失败',
          type: 'error',
          duration: 3000
        })
        return Promise.reject(new Error(res.message || '请求失败'))
      }
      return res
    } else if (res.code !== undefined) {
      // 原有接口使用 code 字段
      if (res.code !== 200) {
        ElMessage({
          message: res.message || '请求失败',
          type: 'error',
          duration: 3000
        })
        return Promise.reject(new Error(res.message || '请求失败'))
      } else {
        return res
      }
    } else {
      // 如果没有明确的状态字段，直接返回
      return res
    }
  },
  error => {
    console.error('请求错误', error, error.response?.data || '无响应数据')
    
    // 根据错误状态码定制错误信息
    let message = '网络错误'
    if (error.response) {
      switch (error.response.status) {
        case 404:
          message = `接口不存在: ${error.config.url}`
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = error.message
      }
    }
    
    ElMessage({
      message,
      type: 'error',
      duration: 3000
    })
    return Promise.reject(error)
  }
)

export default service 