import request from './request'

/**
 * 新建航天器
 */
export function createSatellite(data) {
  return request({
    url: '/satellite/new',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 删除航天器
 */
export function deleteSatellite(id, all = false) {
  return request({
    url: '/satellite/delete',
    method: 'delete',
    params: { id, all }
  })
}

/**
 * 分页条件查询航天器
 */
export function searchSatellites(params) {
  return request({
    url: '/satellite/search',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取航天器
 */
export function getSatelliteById(id) {
  return request({
    url: `/satellite/${id}`,
    method: 'get'
  })
}

/**
 * 更新航天器
 */
export function updateSatellite(data) {
  return request({
    url: '/satellite/update',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 添加批次
 */
export function addBatch(data) {
  return request({
    url: '/satellite/batch',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 查询批次
 */
export function getBatches() {
  return request({
    url: '/satellite/batch',
    method: 'get'
  })
}

/**
 * MQTT连接测试
 */
export function testMqttConnection(data) {
  return request({
    url: '/satellite/mqtttest',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取所有MQTT连接名称
 */
export function getMqttConnections() {
  return request({
    url: '/satellite/mqtttest',
    method: 'get'
  })
}

/**
 * 上传遥测代号数据文件（不绑定航天器）
 */
export function uploadTelemetryFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/satellite/upload/file',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导入遥测代号数据（绑定到指定航天器）
 */
export function importTelemetry(file, satelliteId) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('satelliteId', satelliteId)

  return request({
    url: '/satellite/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 查询遥测代号数据
 */
export function getTelemetryData(params) {
  return request({
    url: '/satellite/telemetry',
    method: 'get',
    params
  })
}

/**
 * 上传遥测代号文件（批次标识）
 */
export function uploadTelemetryBatch(file, fileName) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('fileName', fileName)

  return request({
    url: '/satellite/upload/batch',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 根据文件名查询遥测代号数据
 */
export function getTelemetryByFileName(fileName) {
  return request({
    url: '/satellite/telemetry/batch',
    method: 'get',
    params: { fileName }
  })
}

/**
 * 将临时数据绑定到航天器
 */
export function bindTelemetryToSatellite(satelliteId, fileName) {
  const formData = new FormData()
  formData.append('satelliteId', satelliteId)
  formData.append('fileName', fileName)

  return request({
    url: '/satellite/telemetry/batch/bind',
    method: 'post',
    data: formData
  })
}

/**
 * 根据文件名删除临时遥测代号数据
 */
export function deleteTelemetryByFileName(fileName) {
  return request({
    url: '/satellite/telemetry/batch',
    method: 'delete',
    params: { fileName }
  })
}

/**
 * 获取航天器已绑定的文件信息
 */
export function getBoundFileInfo(satelliteId) {
  return request({
    url: `/satellite/bound-file/${satelliteId}`,
    method: 'get'
  })
}

/**
 * 删除航天器已绑定的遥测数据
 */
export function deleteBoundTelemetryData(satelliteId) {
  return request({
    url: `/satellite/bound-data/${satelliteId}`,
    method: 'delete'
  })
}