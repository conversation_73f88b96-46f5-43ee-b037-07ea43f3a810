<template>
  <div class="cors-test">
    <h2>CORS测试页面</h2>
    
    <div class="test-card">
      <div class="test-header">
        <h3>API连接测试</h3>
      </div>
      
      <div class="test-content">
        <div class="test-buttons">
          <el-button type="primary" @click="testCors">测试CORS配置</el-button>
          <el-button type="success" @click="testMqttApi">测试MQTT API</el-button>
        </div>
        
        <div v-if="testResult" class="test-result">
          <h4>测试结果：</h4>
          <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { getMqttList } from '@/api/mqtt'
import { ElMessage } from 'element-plus'

const testResult = ref(null)

// 测试CORS配置
const testCors = async () => {
  try {
    const response = await axios.get('/cors-test')
    testResult.value = {
      status: 'success',
      data: response.data,
      message: 'CORS配置测试成功'
    }
    ElMessage.success('CORS配置测试成功')
  } catch (error) {
    console.error('CORS测试失败', error)
    testResult.value = {
      status: 'error',
      error: {
        message: error.message,
        response: error.response?.data
      },
      message: 'CORS配置测试失败'
    }
    ElMessage.error('CORS配置测试失败')
  }
}

// 测试MQTT API
const testMqttApi = async () => {
  try {
    const response = await getMqttList({ pageNum: 1, pageSize: 10 })
    testResult.value = {
      status: 'success',
      data: response,
      message: 'MQTT API测试成功'
    }
    ElMessage.success('MQTT API测试成功')
  } catch (error) {
    console.error('MQTT API测试失败', error)
    testResult.value = {
      status: 'error',
      error: {
        message: error.message,
        response: error.response?.data
      },
      message: 'MQTT API测试失败'
    }
    ElMessage.error('MQTT API测试失败')
  }
}
</script>

<style scoped>
.cors-test {
  padding: 20px;
}

.test-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 20px;
}

.test-header {
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
  padding-bottom: 10px;
}

.test-header h3 {
  margin: 0;
  color: #409EFF;
}

.test-buttons {
  margin-bottom: 20px;
}

.test-buttons .el-button {
  margin-right: 15px;
}

.test-result {
  background: #f8f8f8;
  border-radius: 4px;
  padding: 15px;
  margin-top: 20px;
}

.test-result h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

pre {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 