<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1 class="system-title">登录系统</h1>
        <p class="welcome-text">欢迎使用测运控平台</p>
      </div>
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef">
        <el-form-item prop="account">
          <el-input 
            v-model="loginForm.account" 
            placeholder="请输入账号" 
            prefix-icon="User"
            size="large"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码" 
            prefix-icon="Lock"
            show-password
            size="large"
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="loading" 
            class="login-button" 
            size="large"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const router = useRouter()
const loading = ref(false)
const loginFormRef = ref(null)

// 登录表单
const loginForm = reactive({
  account: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  account: [
    { required: true, message: '请输入账号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  loginFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    loading.value = true
    
    try {
      const response = await axios.post('/api/login', loginForm)
      
      if (response.data.code === 200) {
        // 存储token和用户信息
        localStorage.setItem('token', response.data.data.token)
        localStorage.setItem('userInfo', JSON.stringify({
          account: response.data.data.account,
          userId: response.data.data.userId
        }))
        
        ElMessage.success('登录成功')
        router.push('/main')
      } else {
        ElMessage.error(response.data.message || '登录失败')
      }
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error(error.response?.data?.message || '登录失败，请重试')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style scoped>
.login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiB2aWV3Qm94PSIwIDAgMTkyMCAxMDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8ZGVmcz4KPHI+') center/cover no-repeat, 
          radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 35%, #0f0f23 100%);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 20%;
  right: 30%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, #4a90e2 0%, #2e5bba 50%, #1e3a8a 100%);
  border-radius: 50%;
  box-shadow: 
    0 0 50px #4a90e2,
    inset -30px -30px 60px #1e3a8a,
    inset 30px 30px 60px #4a90e2;
  animation: earthRotate 20s linear infinite;
}

.login-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 10% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
    radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 1px, transparent 1px),
    radial-gradient(circle at 40% 40%, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 100px 100px, 150px 150px, 200px 200px;
  animation: starTwinkle 3s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes earthRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes starTwinkle {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

.login-box {
  width: 420px;
  padding: 50px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  z-index: 10;
  position: relative;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.system-title {
  font-weight: 600;
  font-size: 32px;
  color: #1a1a2e;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-text {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #4a90e2 0%, #2e5bba 100%);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #2e5bba 0%, #1e3a8a 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid #e1e8ed;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #4a90e2;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}
</style> 