<template>
  <div class="main-container">
    <!-- 顶部导航栏 -->
    <header class="main-header">
      <div class="header-left">
        <h1 class="platform-title">航天测运控平台</h1>
      </div>
      
      <div class="header-center">
        <div class="datetime-info">
          <div class="date-info">{{ currentDate }}</div>
          <div class="time-info">{{ currentTime }}</div>
        </div>
      </div>
      
      <div class="header-right">
        <el-dropdown @command="handleDropdownCommand">
          <div class="user-info">
            <el-icon class="user-icon"><User /></el-icon>
            <span class="username">{{ userInfo.account }}</span>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile" disabled>个人信息</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="nav-buttons-container">
        <div class="nav-buttons">
          <el-button 
            class="nav-button"
            size="large"
            @click="handleNavigation('spacecraft-monitor')"
          >
            航天器监视
          </el-button>
          
          <el-button 
            class="nav-button"
            size="large"
            @click="handleNavigation('intelligent-detection')"
          >
            智能检测
          </el-button>
          
          <el-button 
            class="nav-button"
            size="large"
            @click="handleNavigation('envelope-analysis')"
          >
            包络智能分析
          </el-button>
          
          <el-button 
            class="nav-button"
            size="large"
            @click="handleNavigation('health-evaluation')"
          >
            在轨健康评价
          </el-button>
          
          <el-button 
            class="nav-button system-management"
            type="primary"
            size="large"
            @click="handleNavigation('system-management')"
          >
            系统管理
          </el-button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, ArrowDown } from '@element-plus/icons-vue'

const router = useRouter()
const currentDate = ref('')
const currentTime = ref('')
const userInfo = ref({})

// 获取用户信息
const getUserInfo = () => {
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    userInfo.value = JSON.parse(storedUserInfo)
  } else {
    // 如果没有用户信息，跳转到登录页
    router.push('/login')
  }
}

// 更新时间
const updateDateTime = () => {
  const now = new Date()
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  
  currentDate.value = `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日 ${days[now.getDay()]}`
  currentTime.value = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
}

let timeInterval = null

// 下拉菜单处理
const handleDropdownCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch {
      // 用户取消退出
    }
  } else if (command === 'profile') {
    ElMessage.info('个人信息功能暂未实现')
  }
}

// 导航处理
const handleNavigation = (type) => {
  switch (type) {
    case 'system-management':
      // 跳转到系统管理页面
      router.push('/system/spacecraft/list')
      break
    case 'spacecraft-monitor':
    case 'intelligent-detection':
    case 'envelope-analysis':
    case 'health-evaluation':
      ElMessage.info('该功能暂未实现')
      break
    default:
      break
  }
}

onMounted(() => {
  getUserInfo()
  updateDateTime()
  // 每秒更新时间
  timeInterval = setInterval(updateDateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.main-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.main-header {
  height: 80px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left .platform-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a2e;
  margin: 0;
}

.header-center .datetime-info {
  text-align: center;
}

.date-info {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.time-info {
  font-size: 20px;
  font-weight: 600;
  color: #4a90e2;
  font-family: 'Courier New', monospace;
}

.header-right .user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: rgba(74, 144, 226, 0.1);
}

.user-icon {
  font-size: 20px;
  color: #4a90e2;
  margin-right: 8px;
}

.username {
  font-size: 16px;
  color: #333;
  margin-right: 8px;
}

.dropdown-icon {
  font-size: 14px;
  color: #666;
}

.main-content {
  height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.nav-buttons-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 60px;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.nav-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  max-width: 600px;
}

.nav-button {
  width: 250px;
  height: 80px;
  font-size: 18px;
  font-weight: 500;
  border-radius: 12px;
  border: 2px solid #e1e8ed;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #4a5568;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(74, 144, 226, 0.2);
  border-color: #4a90e2;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  color: #2d3748;
}

.system-management {
  grid-column: 1 / -1;
  justify-self: center;
  background: linear-gradient(135deg, #4a90e2 0%, #2e5bba 100%) !important;
  color: white !important;
  border: none !important;
}

.system-management:hover {
  background: linear-gradient(135deg, #2e5bba 0%, #1e3a8a 100%) !important;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(74, 144, 226, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-header {
    height: 60px;
    padding: 0 20px;
  }
  
  .platform-title {
    font-size: 18px !important;
  }
  
  .date-info {
    font-size: 12px !important;
  }
  
  .time-info {
    font-size: 16px !important;
  }
  
  .nav-buttons-container {
    padding: 30px;
  }
  
  .nav-buttons {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .nav-button {
    width: 100%;
    height: 60px;
    font-size: 16px;
  }
}
</style> 