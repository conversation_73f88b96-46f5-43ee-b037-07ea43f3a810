<template>
  <div class="config-main-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>
          <el-icon><Setting /></el-icon>
          单机配置
        </el-breadcrumb-item>
        <el-breadcrumb-item>配置遥测代号</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <div class="action-buttons">
        <el-button type="primary" @click="handleAddSubsystem">添加分系统</el-button>
        <el-button type="primary" @click="handleAddSingle">添加单机</el-button>
        <el-button @click="handleAddModule">添加模块</el-button>
        <el-button type="danger" plain @click="handleDelete" :disabled="!canDelete">删除</el-button>
        <el-button type="primary" plain @click="handleBindTelemetry" :disabled="!currentSelectedNode.data || currentSelectedNode.data.type === 'satellite'">绑定遥测代号</el-button>
      </div>
      <div class="return-button">
        <el-button type="primary" @click="handleReturn">返回</el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧树形组件 -->
      <div class="tree-section">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          class="config-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span v-if="data.type === 'satellite'" class="node-icon">🛰️</span>
              <span v-else-if="data.type === 'subsystem'" class="node-icon">📁</span>
              <span v-else-if="data.type === 'single'" class="node-icon">💻</span>
              <span v-else-if="data.type === 'module'" class="node-icon">📄</span>
              <span class="node-label" @dblclick="handleNodeEdit(node, data)">{{ node.label }}</span>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 右侧遥测代号表 -->
      <div class="table-section">
        <div class="table-header">
          <h3>遥测代号表</h3>
        </div>
        <el-table
          :data="telemetryData"
          border
          style="width: 100%"
          empty-text="暂无数据"
          @row-click="handleTelemetryRowClick"
          :row-class-name="getTelemetryRowClass"
        >
          <el-table-column prop="serialNum" label="序号" width="80" />
          <el-table-column prop="name" label="遥测代号" width="150" />
          <el-table-column prop="description" label="代号描述" />
          <el-table-column prop="note" label="备注" />
        </el-table>
      </div>
    </div>

    <!-- 遥测代号绑定对话框 -->
    <el-dialog
      v-model="bindDialog.visible"
      title="绑定遥测代号"
      width="60%"
      :close-on-click-modal="false"
    >
      <div class="bind-dialog-content">
        <div class="bind-info">
          <p><strong>绑定对象：</strong>{{ bindDialog.targetName }} ({{ bindDialog.levelText }})</p>
        </div>
        <div class="bind-table">
          <h4>可绑定的遥测代号</h4>
          <el-table
            :data="bindDialog.availableCodes"
            border
            style="width: 100%"
            @row-click="handleBindRowClick"
            :row-class-name="getBindRowClass"
            max-height="400"
          >
            <el-table-column prop="serialNum" label="序号" width="80" />
            <el-table-column prop="name" label="遥测代号" width="150" />
            <el-table-column prop="description" label="代号描述" />
            <el-table-column prop="note" label="备注" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="bindDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmBind" :disabled="bindDialog.selectedCodes.length === 0">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  ArrowRight
} from '@element-plus/icons-vue'
import {
  getSubsystemsBySatelliteId,
  getSinglesBySubsystemId,
  getModulesBySingleId,
  addSubsystem,
  addSingle,
  addModule,
  updateSubsystem,
  updateSingle,
  updateModule,
  deleteSubsystem,
  deleteSingle,
  deleteModule,
  searchTelemetryCodes,
  getBoundTelemetryCodes,
  getAvailableTelemetryCodes,
  bindTelemetryCode,
  unbindTelemetryCode,
  unbindSpecificTelemetryCode,
  unbindTelemetryCodeBySerialNum
} from '@/api/single'

const route = useRoute()
const router = useRouter()

// 树形组件引用
const treeRef = ref()

// 树形数据
const treeData = ref([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 遥测代号表数据
const telemetryData = ref([])

// 选中的遥测代号行
const selectedTelemetryRow = ref(null)

// 绑定对话框数据
const bindDialog = reactive({
  visible: false,
  targetName: '',
  levelText: '',
  level: '',
  id: null,
  availableCodes: [],
  selectedCodes: []
})

// 当前选中的航天器信息
const currentSatellite = reactive({
  id: null,
  name: ''
})

// 当前选中的节点信息
const currentSelectedNode = reactive({
  data: null,
  node: null
})

// 编辑状态
const editingNode = ref(null)

// 计算属性：是否可以删除
const canDelete = computed(() => {
  // 如果选中了遥测代号行，可以删除遥测代号
  if (selectedTelemetryRow.value) {
    return true
  }
  // 如果选中了树节点且不是航天器节点，可以删除节点
  if (currentSelectedNode.data && currentSelectedNode.data.type !== 'satellite') {
    return true
  }
  return false
})

// 初始化页面数据
const initPageData = () => {
  // 从路由参数获取航天器信息
  const satelliteId = route.query.satelliteId
  const satelliteName = route.query.satelliteName
  
  if (satelliteId && satelliteName) {
    currentSatellite.id = satelliteId
    currentSatellite.name = satelliteName
    
    // 初始化树形数据
    treeData.value = [{
      id: `satellite_${satelliteId}`,
      name: satelliteName,
      type: 'satellite',
      satelliteId: satelliteId,
      children: []
    }]
    
    // 加载分系统数据
    loadSubsystems(satelliteId)

    // 加载遥测代号数据
    loadTelemetryData()
  } else {
    ElMessage.error('缺少航天器信息')
    handleReturn()
  }
}

// 加载分系统数据
const loadSubsystems = async (satelliteId) => {
  try {
    const response = await getSubsystemsBySatelliteId(satelliteId)
    if (response.code === 200) {
      const subsystems = response.data || []
      const satelliteNode = treeData.value[0]
      satelliteNode.children = subsystems.map(subsystem => ({
        id: `subsystem_${subsystem.id}`,
        name: subsystem.name,
        type: 'subsystem',
        subsystemId: subsystem.id,
        satelliteId: satelliteId,
        rawData: subsystem,
        children: []
      }))
      console.log('加载到', subsystems.length, '个分系统')
    }
  } catch (error) {
    console.error('加载分系统失败:', error)
    ElMessage.error('加载分系统失败')
  }
}

// 加载单机数据
const loadSingles = async (subsystemId, subsystemNode) => {
  try {
    const response = await getSinglesBySubsystemId(subsystemId)
    if (response.code === 200) {
      const singles = response.data || []
      subsystemNode.children = singles.map(single => ({
        id: `single_${single.id}`,
        name: single.name,
        type: 'single',
        singleId: single.id,
        subsystemId: subsystemId,
        rawData: single,
        children: []
      }))
      console.log('加载到', singles.length, '个单机')
    }
  } catch (error) {
    console.error('加载单机失败:', error)
    ElMessage.error('加载单机失败')
  }
}

// 加载模块数据
const loadModules = async (singleId, singleNode) => {
  try {
    const response = await getModulesBySingleId(singleId)
    if (response.code === 200) {
      const modules = response.data || []
      singleNode.children = modules.map(module => ({
        id: `module_${module.id}`,
        name: module.name,
        type: 'module',
        moduleId: module.id,
        singleId: singleId,
        rawData: module,
        children: []
      }))
      console.log('加载到', modules.length, '个模块')
    }
  } catch (error) {
    console.error('加载模块失败:', error)
    ElMessage.error('加载模块失败')
  }
}

// 树节点点击事件
const handleNodeClick = async (data, node) => {
  console.log('节点点击:', data, node)
  currentSelectedNode.data = data
  currentSelectedNode.node = node

  // 根据节点类型加载对应的子节点
  if (data.type === 'satellite') {
    // 点击航天器，加载分系统
    if (!data.children || data.children.length === 0) {
      await loadSubsystems(data.satelliteId)
    }
  } else if (data.type === 'subsystem') {
    // 点击分系统，加载单机
    if (!data.children || data.children.length === 0) {
      await loadSingles(data.subsystemId, data)
    }
  } else if (data.type === 'single') {
    // 点击单机，加载模块
    if (!data.children || data.children.length === 0) {
      await loadModules(data.singleId, data)
    }
  }

  // 清空遥测代号选中状态
  selectedTelemetryRow.value = null

  // 如果点击的是分系统、单机或模块，显示其绑定的遥测代号
  if (data.type !== 'satellite') {
    await loadNodeTelemetryData(data)
  } else {
    // 如果点击的是航天器，显示航天器的所有遥测代号
    await loadTelemetryData()
  }
}

// 树节点双击编辑事件
const handleNodeEdit = async (node, data) => {
  if (data.type === 'satellite') {
    ElMessage.info('航天器名称不可编辑')
    return
  }

  console.log('编辑节点:', node, data)

  try {
    const { value: newName } = await ElMessageBox.prompt(
      `请输入新的${getNodeTypeName(data.type)}名称`,
      `编辑${getNodeTypeName(data.type)}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: data.name,
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '名称不能为空'
          }
          return true
        }
      }
    )

    if (newName && newName.trim() !== data.name) {
      await updateNodeName(data, newName.trim())
    }
  } catch (error) {
    // 用户取消编辑
    console.log('取消编辑')
  }
}

// 获取节点类型中文名称
const getNodeTypeName = (type) => {
  const typeMap = {
    'subsystem': '分系统',
    'single': '单机',
    'module': '模块'
  }
  return typeMap[type] || type
}

// 更新节点名称
const updateNodeName = async (data, newName) => {
  try {
    const updateData = {
      name: newName
    }

    if (data.type === 'subsystem') {
      updateData.spacecraftId = data.satelliteId
      await updateSubsystem(data.subsystemId, updateData)
    } else if (data.type === 'single') {
      updateData.subsystemId = data.subsystemId
      updateData.spacecraftId = currentSatellite.id
      await updateSingle(data.singleId, updateData)
    } else if (data.type === 'module') {
      updateData.subsystemId = data.subsystemId
      updateData.spacecraftId = currentSatellite.id
      updateData.singleId = data.singleId
      await updateModule(data.moduleId, updateData)
    }

    // 更新本地数据
    data.name = newName
    if (data.rawData) {
      data.rawData.name = newName
    }

    ElMessage.success('编辑成功')
  } catch (error) {
    console.error('编辑失败:', error)
    ElMessage.error('编辑失败：' + (error.message || '未知错误'))
  }
}

// 添加分系统
const handleAddSubsystem = async () => {
  if (!currentSatellite.id) {
    ElMessage.error('请先选择航天器')
    return
  }

  try {
    const { value: name } = await ElMessageBox.prompt(
      '请输入分系统名称',
      '添加分系统',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '分系统名称不能为空'
          }
          return true
        }
      }
    )

    if (name && name.trim()) {
      const addData = {
        name: name.trim(),
        spacecraftId: currentSatellite.id
      }

      const response = await addSubsystem(addData)
      if (response.code === 200) {
        ElMessage.success('分系统添加成功')
        // 重新加载分系统数据
        await loadSubsystems(currentSatellite.id)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('添加分系统失败:', error)
      ElMessage.error('添加分系统失败：' + (error.message || '未知错误'))
    }
  }
}

// 添加单机
const handleAddSingle = async () => {
  if (!currentSelectedNode.data || currentSelectedNode.data.type !== 'subsystem') {
    ElMessage.error('请先选择一个分系统')
    return
  }

  try {
    const { value: name } = await ElMessageBox.prompt(
      '请输入单机名称',
      '添加单机',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '单机名称不能为空'
          }
          return true
        }
      }
    )

    if (name && name.trim()) {
      const addData = {
        name: name.trim(),
        subsystemId: currentSelectedNode.data.subsystemId,
        spacecraftId: currentSatellite.id
      }

      const response = await addSingle(addData)
      if (response.code === 200) {
        ElMessage.success('单机添加成功')
        // 重新加载单机数据
        await loadSingles(currentSelectedNode.data.subsystemId, currentSelectedNode.data)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('添加单机失败:', error)
      ElMessage.error('添加单机失败：' + (error.message || '未知错误'))
    }
  }
}

// 添加模块
const handleAddModule = async () => {
  if (!currentSelectedNode.data || currentSelectedNode.data.type !== 'single') {
    ElMessage.error('请先选择一个单机')
    return
  }

  try {
    const { value: name } = await ElMessageBox.prompt(
      '请输入模块名称',
      '添加模块',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '模块名称不能为空'
          }
          return true
        }
      }
    )

    if (name && name.trim()) {
      const addData = {
        name: name.trim(),
        subsystemId: currentSelectedNode.data.subsystemId,
        spacecraftId: currentSatellite.id,
        singleId: currentSelectedNode.data.singleId
      }

      const response = await addModule(addData)
      if (response.code === 200) {
        ElMessage.success('模块添加成功')
        // 重新加载模块数据
        await loadModules(currentSelectedNode.data.singleId, currentSelectedNode.data)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('添加模块失败:', error)
      ElMessage.error('添加模块失败：' + (error.message || '未知错误'))
    }
  }
}

// 删除功能
const handleDelete = async () => {
  // 优先删除选中的遥测代号
  if (selectedTelemetryRow.value) {
    await handleDeleteTelemetryCode()
    return
  }

  // 删除选中的树节点
  if (!currentSelectedNode.data) {
    ElMessage.error('请先选择要删除的节点')
    return
  }

  const nodeData = currentSelectedNode.data
  const nodeType = nodeData.type

  // 不允许删除航天器节点
  if (nodeType === 'satellite') {
    ElMessage.error('不能删除航天器节点')
    return
  }

  let confirmMessage = ''
  let deleteAction = null

  switch (nodeType) {
    case 'subsystem':
      confirmMessage = `确定要删除分系统 "${nodeData.name}" 吗？删除后将级联删除其下所有单机和模块，此操作不可恢复！`
      deleteAction = () => deleteSubsystem(nodeData.subsystemId)
      break
    case 'single':
      confirmMessage = `确定要删除单机 "${nodeData.name}" 吗？删除后将级联删除其下所有模块，此操作不可恢复！`
      deleteAction = () => deleteSingle(nodeData.singleId)
      break
    case 'module':
      confirmMessage = `确定要删除模块 "${nodeData.name}" 吗？此操作不可恢复！`
      deleteAction = () => deleteModule(nodeData.moduleId)
      break
    default:
      ElMessage.error('未知的节点类型')
      return
  }

  try {
    await ElMessageBox.confirm(
      confirmMessage,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 执行删除操作
    const response = await deleteAction()
    if (response.code === 200) {
      ElMessage.success('删除成功')

      // 重新加载树形数据
      await initPageData()

      // 清空当前选中节点
      currentSelectedNode.data = null
      currentSelectedNode.node = null
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败：' + (error.message || '未知错误'))
    }
  }
}

// ==================== 遥测代号相关方法 ====================

// 加载遥测代号数据
const loadTelemetryData = async () => {
  if (!currentSatellite.id) return

  try {
    const response = await searchTelemetryCodes(currentSatellite.id)
    if (response.code === 200) {
      telemetryData.value = response.data || []
    } else {
      console.error('加载遥测代号失败:', response.message)
    }
  } catch (error) {
    console.error('加载遥测代号失败:', error)
  }
}

// 加载节点绑定的遥测代号数据
const loadNodeTelemetryData = async (nodeData) => {
  const level = nodeData.type
  const id = getNodeId(nodeData)

  if (!id) {
    console.error('无法获取节点ID')
    return
  }

  try {
    const response = await getBoundTelemetryCodes(level, id)
    if (response.code === 200) {
      telemetryData.value = response.data || []
    } else {
      console.error('加载节点遥测代号失败:', response.message)
      telemetryData.value = []
    }
  } catch (error) {
    console.error('加载节点遥测代号失败:', error)
    telemetryData.value = []
  }
}

// 遥测代号表格行点击
const handleTelemetryRowClick = (row) => {
  selectedTelemetryRow.value = row
}

// 获取遥测代号表格行样式
const getTelemetryRowClass = ({ row }) => {
  return selectedTelemetryRow.value && selectedTelemetryRow.value.id === row.id ? 'selected-row' : ''
}

// 绑定遥测代号按钮点击
const handleBindTelemetry = async () => {
  if (!currentSelectedNode.data) {
    ElMessage.error('请先选择要绑定的节点')
    return
  }

  const nodeData = currentSelectedNode.data
  const nodeType = nodeData.type

  if (nodeType === 'satellite') {
    ElMessage.error('航天器节点不支持绑定遥测代号')
    return
  }

  // 设置对话框数据
  bindDialog.level = nodeType
  bindDialog.id = getNodeId(nodeData)
  bindDialog.targetName = nodeData.name
  bindDialog.levelText = getLevelText(nodeType)
  bindDialog.selectedCodes = []

  try {
    // 加载可绑定的遥测代号
    const response = await getAvailableTelemetryCodes(nodeType, bindDialog.id)
    if (response.code === 200) {
      bindDialog.availableCodes = response.data || []
      bindDialog.visible = true
    } else {
      ElMessage.error('加载可绑定遥测代号失败：' + response.message)
    }
  } catch (error) {
    console.error('加载可绑定遥测代号失败:', error)
    ElMessage.error('加载可绑定遥测代号失败')
  }
}

// 绑定对话框表格行点击
const handleBindRowClick = (row) => {
  const index = bindDialog.selectedCodes.findIndex(code => code.id === row.id)
  if (index > -1) {
    bindDialog.selectedCodes.splice(index, 1)
  } else {
    bindDialog.selectedCodes.push(row)
  }
}

// 获取绑定对话框表格行样式
const getBindRowClass = ({ row }) => {
  return bindDialog.selectedCodes.some(code => code.id === row.id) ? 'selected-row' : ''
}

// 确认绑定
const handleConfirmBind = async () => {
  if (bindDialog.selectedCodes.length === 0) {
    ElMessage.error('请选择要绑定的遥测代号')
    return
  }

  try {
    // 逐个绑定选中的遥测代号
    for (const code of bindDialog.selectedCodes) {
      const bindData = {
        level: bindDialog.level,
        id: bindDialog.id,
        telemetryCodeId: code.id
      }

      const response = await bindTelemetryCode(bindData)
      if (response.code !== 200) {
        ElMessage.error(`绑定遥测代号 ${code.name} 失败：${response.message}`)
        return
      }
    }

    ElMessage.success('遥测代号绑定成功')
    bindDialog.visible = false

    // 重新加载遥测代号数据
    await loadTelemetryData()
  } catch (error) {
    console.error('绑定遥测代号失败:', error)
    ElMessage.error('绑定遥测代号失败')
  }
}

// 获取节点ID
const getNodeId = (nodeData) => {
  switch (nodeData.type) {
    case 'subsystem':
      return nodeData.subsystemId
    case 'single':
      return nodeData.singleId
    case 'module':
      return nodeData.moduleId
    default:
      return null
  }
}

// 获取层级文本
const getLevelText = (level) => {
  switch (level) {
    case 'subsystem':
      return '分系统'
    case 'single':
      return '单机'
    case 'module':
      return '模块'
    default:
      return '未知'
  }
}

// 删除遥测代号
const handleDeleteTelemetryCode = async () => {
  if (!selectedTelemetryRow.value) {
    ElMessage.error('请先选择要删除的遥测代号')
    return
  }

  const telemetryCode = selectedTelemetryRow.value

  // 检查是否选中了具体的层级节点
  if (!currentSelectedNode.data || currentSelectedNode.data.type === 'satellite') {
    ElMessage.error('请先选择分系统、单机或模块节点')
    return
  }

  const nodeData = currentSelectedNode.data
  let level = nodeData.type
  let levelId
  let nodeName = nodeData.name

  switch (level) {
    case 'subsystem':
      levelId = nodeData.subsystemId
      break
    case 'single':
      levelId = nodeData.singleId
      break
    case 'module':
      levelId = nodeData.moduleId
      break
    default:
      ElMessage.error('不支持的节点类型')
      return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除遥测代号 "${telemetryCode.name}" (序号: ${telemetryCode.serialNum}) 在${level === 'subsystem' ? '分系统' : level === 'single' ? '单机' : '模块'} "${nodeName}" 中的绑定吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 根据层级和遥测代号序号删除绑定
    const response = await unbindTelemetryCodeBySerialNum(level, levelId, telemetryCode.serialNum)

    if (response.code === 200) {
      ElMessage.success('遥测代号绑定删除成功')

      // 重新加载当前显示的遥测代号数据
      await loadNodeTelemetryData(currentSelectedNode.data)

      // 清空选中状态
      selectedTelemetryRow.value = null
    } else {
      ElMessage.error(response.message || '删除遥测代号绑定失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除遥测代号绑定失败:', error)
      ElMessage.error('删除遥测代号绑定失败：' + (error.message || '未知错误'))
    }
  }
}



// 返回按钮
const handleReturn = () => {
  // 清空遥测代号数据
  telemetryData.value = []
  selectedTelemetryRow.value = null
  router.push('/system/spacecraft/config')
}

// 页面加载时初始化
onMounted(() => {
  initPageData()
})
</script>

<style scoped>
.config-main-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.breadcrumb-section {
  margin-bottom: 20px;
}

.breadcrumb-section :deep(.el-breadcrumb__item) {
  font-size: 16px;
}

.breadcrumb-section :deep(.el-breadcrumb__inner) {
  display: flex;
  align-items: center;
  gap: 5px;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.main-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.tree-section {
  width: 300px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.table-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.table-header {
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.config-tree {
  width: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-icon {
  font-size: 16px;
  margin-right: 4px;
  display: inline-block;
  width: 20px;
  text-align: center;
}

.node-label {
  flex: 1;
  cursor: pointer;
}

.node-label:hover {
  color: #409EFF;
}

/* 树形组件样式优化 */
:deep(.el-tree-node__content) {
  height: 32px;
  padding: 0 8px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node__expand-icon) {
  color: #409EFF;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: #e6f7ff !important;
}

:deep(.selected-row:hover) {
  background-color: #bae7ff !important;
}

/* 绑定对话框样式 */
.bind-dialog-content {
  padding: 10px 0;
}

.bind-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.bind-info p {
  margin: 0;
  color: #606266;
}

.bind-table h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  text-align: right;
}
</style>
