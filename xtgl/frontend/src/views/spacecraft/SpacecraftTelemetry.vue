<template>
  <div class="telemetry-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item v-if="route.query.from === 'list'">
          <el-icon><List /></el-icon>
          航天器列表
        </el-breadcrumb-item>
        <el-breadcrumb-item v-else>
          <el-icon><Setting /></el-icon>
          单机配置
        </el-breadcrumb-item>
        <el-breadcrumb-item>查看遥测代号</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 标题区域 -->
    <div class="title-container">
      <div class="section-title">
        <div class="divider"></div>
        <h3>{{ satelliteName }} - 遥测代号</h3>
      </div>
      <div class="title-buttons">
        <el-button type="primary" @click="handleReturn">返回</el-button>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 左侧树形控件 -->
      <div class="tree-container">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="defaultProps"
          highlight-current
          @node-click="handleNodeClick"
        />
      </div>
      
      <!-- 右侧表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
          height="calc(100vh - 250px)"
        >
          <el-table-column prop="serialNum" label="序号" width="80" />
          <el-table-column prop="name" label="遥测代号" min-width="120" />
          <el-table-column prop="description" label="代号描述" min-width="200" />
          <el-table-column prop="note" label="备注" min-width="150" />
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.pageNo"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Setting, ArrowRight, List } from '@element-plus/icons-vue'
import {
  getSubsystemsBySatelliteId,
  getSinglesBySubsystemId,
  getModulesBySingleId,
  searchTelemetryCodes,
  getBoundTelemetryCodes
} from '@/api/single'
import { getSatelliteById } from '@/api/satellite'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const satelliteId = route.params.id
const tableData = ref([])
const treeRef = ref(null)
const satelliteName = ref('航天器')

// 分页配置
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

// 树形控件配置
const defaultProps = {
  children: 'children',
  label: 'name',
}

// 树形数据
const treeData = ref([])

// 当前选中的节点
const currentSelectedNode = reactive({
  data: null,
  node: null
})

// 初始化数据
onMounted(async () => {
  await initPageData()
})

// 初始化页面数据
const initPageData = async () => {
  await fetchSatelliteInfo()
  await initTreeData()
  await loadTelemetryData() // 默认加载航天器的遥测代号
}

// 获取航天器信息
const fetchSatelliteInfo = async () => {
  try {
    const res = await getSatelliteById(satelliteId)
    satelliteName.value = res.data.name || '航天器'
  } catch (error) {
    console.error('获取航天器信息失败', error)
    ElMessage.error('获取航天器信息失败')
  }
}

// 初始化树形数据
const initTreeData = async () => {
  try {
    // 创建航天器根节点
    const satelliteNode = {
      type: 'satellite',
      satelliteId: parseInt(satelliteId),
      name: satelliteName.value,
      children: []
    }

    // 加载分系统
    await loadSubsystems(satelliteNode)

    treeData.value = [satelliteNode]

    // 默认选中航天器节点
    currentSelectedNode.data = satelliteNode
  } catch (error) {
    console.error('初始化树形数据失败', error)
    ElMessage.error('初始化数据失败')
  }
}

// 加载分系统
const loadSubsystems = async (satelliteNode) => {
  try {
    const response = await getSubsystemsBySatelliteId(satelliteNode.satelliteId)
    if (response.code === 200 && response.data) {
      satelliteNode.children = response.data.map(subsystem => ({
        type: 'subsystem',
        subsystemId: subsystem.id,
        name: subsystem.name,
        children: []
      }))
    }
  } catch (error) {
    console.error('加载分系统失败', error)
  }
}

// 加载单机
const loadSingles = async (subsystemId, subsystemNode) => {
  try {
    const response = await getSinglesBySubsystemId(subsystemId)
    if (response.code === 200 && response.data) {
      subsystemNode.children = response.data.map(single => ({
        type: 'single',
        singleId: single.id,
        name: single.name,
        children: []
      }))
    }
  } catch (error) {
    console.error('加载单机失败', error)
  }
}

// 加载模块
const loadModules = async (singleId, singleNode) => {
  try {
    const response = await getModulesBySingleId(singleId)
    if (response.code === 200 && response.data) {
      singleNode.children = response.data.map(module => ({
        type: 'module',
        moduleId: module.id,
        name: module.name,
        children: []
      }))
    }
  } catch (error) {
    console.error('加载模块失败', error)
  }
}

// 加载遥测代号数据
const loadTelemetryData = async () => {
  loading.value = true
  try {
    const response = await searchTelemetryCodes(satelliteId)
    if (response.code === 200 && response.data) {
      tableData.value = response.data
      pagination.total = response.data.length
    } else {
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载遥测代号数据失败', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 加载节点的遥测代号数据
const loadNodeTelemetryData = async (nodeData) => {
  loading.value = true
  try {
    let response
    switch (nodeData.type) {
      case 'satellite':
        response = await searchTelemetryCodes(nodeData.satelliteId)
        break
      case 'subsystem':
        response = await getBoundTelemetryCodes('subsystem', nodeData.subsystemId)
        break
      case 'single':
        response = await getBoundTelemetryCodes('single', nodeData.singleId)
        break
      case 'module':
        response = await getBoundTelemetryCodes('module', nodeData.moduleId)
        break
      default:
        throw new Error('不支持的节点类型')
    }

    if (response.code === 200 && response.data) {
      tableData.value = response.data
      pagination.total = response.data.length
    } else {
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载节点遥测代号数据失败', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 处理树节点点击
const handleNodeClick = async (data, node) => {
  console.log('节点点击:', data, node)
  currentSelectedNode.data = data
  currentSelectedNode.node = node

  // 根据节点类型加载对应的子节点
  if (data.type === 'satellite') {
    // 点击航天器，加载分系统（如果还没有加载）
    if (!data.children || data.children.length === 0) {
      await loadSubsystems(data)
    }
  } else if (data.type === 'subsystem') {
    // 点击分系统，加载单机
    if (!data.children || data.children.length === 0) {
      await loadSingles(data.subsystemId, data)
    }
  } else if (data.type === 'single') {
    // 点击单机，加载模块
    if (!data.children || data.children.length === 0) {
      await loadModules(data.singleId, data)
    }
  }

  // 加载对应的遥测代号数据
  await loadNodeTelemetryData(data)
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.pageNo = 1
  // 重新加载当前节点的数据
  if (currentSelectedNode.data) {
    loadNodeTelemetryData(currentSelectedNode.data)
  } else {
    loadTelemetryData()
  }
}

// 分页页码变化
const handleCurrentChange = (val) => {
  pagination.pageNo = val
  // 重新加载当前节点的数据
  if (currentSelectedNode.data) {
    loadNodeTelemetryData(currentSelectedNode.data)
  } else {
    loadTelemetryData()
  }
}

// 返回按钮
const handleReturn = () => {
  const from = route.query.from
  if (from === 'list') {
    router.push('/system/spacecraft/list')
  } else if (from === 'config') {
    router.push('/system/spacecraft/config')
  } else {
    // 默认返回航天器列表
    router.push('/system/spacecraft/list')
  }
}
</script>

<style scoped>
.telemetry-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.breadcrumb-section {
  margin-bottom: 20px;
}

.breadcrumb-section :deep(.el-breadcrumb__item) {
  font-size: 16px;
}

.breadcrumb-section :deep(.el-breadcrumb__inner) {
  display: flex;
  align-items: center;
  gap: 5px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-buttons {
  display: flex;
  gap: 10px;
}

.section-title {
  display: flex;
  align-items: center;
}

.section-title .divider {
  width: 4px;
  height: 16px;
  background-color: #409EFF;
  margin-right: 8px;
  border-radius: 2px;
}

.section-title h3 {
  font-size: 16px;
  margin: 0;
  font-weight: bold;
}

.content-container {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
}

.tree-container {
  width: 300px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: auto;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 10px 0;
  border-radius: 4px;
}
</style> 