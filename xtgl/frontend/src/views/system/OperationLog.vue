<template>
  <div class="log-container">
    <h2 class="page-title">操作日志</h2>

    <!-- 搜索过滤区域 -->
    <div class="filter-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="filter-item">
            <span>用户姓名：</span>
            <el-input
              v-model="searchForm.userName"
              placeholder="请输入用户姓名"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #suffix>
                <el-icon class="search-icon" @click="handleSearch">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="filter-item">
            <span>操作时间：</span>
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleTimeRangeChange"
            />
          </div>
        </el-col>
        <el-col :span="6">
          <div class="filter-buttons">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="createTime" label="时间" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="200" />
        <el-table-column prop="ipAddress" label="IP地址" width="140" />
        <el-table-column prop="requestMethod" label="请求方式" width="100" />
        <el-table-column prop="durationMs" label="耗时" width="100">
          <template #default="scope">
            {{ scope.row.durationMs }}ms
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              type="text"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="操作日志详情"
      width="70%"
      :before-close="handleCloseDetail"
      class="log-detail-dialog"
    >
      <div v-if="currentLogDetail" class="detail-content">
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">请求时间：</span>
                <span class="detail-value">{{ formatDateTime(currentLogDetail.requestTime) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">操作人：</span>
                <span class="detail-value">{{ currentLogDetail.operator }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">请求地址：</span>
                <span class="detail-value">{{ currentLogDetail.requestUrl }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">标题：</span>
                <span class="detail-value">{{ currentLogDetail.title }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">IP地址：</span>
                <span class="detail-value ip-address">{{ currentLogDetail.ipAddress }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">请求方式：</span>
                <span class="detail-value method-tag" :class="getMethodClass(currentLogDetail.requestMethod)">
                  {{ currentLogDetail.requestMethod }}
                </span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">客户端：</span>
                <span class="detail-value">{{ currentLogDetail.client }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">耗时：</span>
                <span class="detail-value duration" :class="getDurationClass(currentLogDetail.durationMs)">
                  {{ currentLogDetail.durationMs }}ms
                </span>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="detail-section">
          <h4 class="section-title">浏览器信息</h4>
          <div class="detail-item full-width">
            <span class="detail-label">浏览器：</span>
            <span class="detail-value user-agent">{{ formatUserAgent(currentLogDetail.userAgent) }}</span>
          </div>
        </div>

        <div class="detail-section" v-if="currentLogDetail.requestParam">
          <h4 class="section-title">请求参数</h4>
          <div class="request-param-container">
            <pre class="request-param">{{ formatRequestParam(currentLogDetail.requestParam) }}</pre>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDetail" size="large">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { searchLogs } from '@/api/log'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const detailDialogVisible = ref(false)
const currentLogDetail = ref(null)
const timeRange = ref([])

// 搜索表单
const searchForm = reactive({
  userName: '',
  startTime: '',
  endTime: ''
})

// 分页信息
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

// 生命周期钩子
onMounted(() => {
  loadLogData()
})

// 加载日志数据
const loadLogData = async () => {
  loading.value = true
  try {
    const params = {
      userName: searchForm.userName || undefined,
      startTime: searchForm.startTime || undefined,
      endTime: searchForm.endTime || undefined,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize
    }

    const response = await searchLogs(params)
    if (response.code === 200) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '加载日志数据失败')
    }
  } catch (error) {
    console.error('加载日志数据失败:', error)
    ElMessage.error('加载日志数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.pageNo = 1
  loadLogData()
}

// 重置处理
const handleReset = () => {
  searchForm.userName = ''
  searchForm.startTime = ''
  searchForm.endTime = ''
  timeRange.value = []
  pagination.pageNo = 1
  loadLogData()
}

// 时间范围变化处理
const handleTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    searchForm.startTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 查看详情
const handleViewDetail = (row) => {
  currentLogDetail.value = row
  detailDialogVisible.value = true
}

// 关闭详情对话框
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentLogDetail.value = null
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNo = 1
  loadLogData()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.pageNo = page
  loadLogData()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化请求参数
const formatRequestParam = (param) => {
  if (!param) return '无'
  try {
    return JSON.stringify(JSON.parse(param), null, 2)
  } catch (e) {
    return param
  }
}

// 格式化用户代理信息
const formatUserAgent = (userAgent) => {
  if (!userAgent) return '未知浏览器'

  // 简化用户代理字符串显示
  if (userAgent.includes('Chrome')) {
    const chromeMatch = userAgent.match(/Chrome\/([0-9.]+)/)
    const version = chromeMatch ? chromeMatch[1] : '未知版本'
    if (userAgent.includes('Edg')) {
      const edgeMatch = userAgent.match(/Edg\/([0-9.]+)/)
      const edgeVersion = edgeMatch ? edgeMatch[1] : '未知版本'
      return `Microsoft Edge ${edgeVersion} (基于 Chrome ${version})`
    }
    return `Google Chrome ${version}`
  } else if (userAgent.includes('Firefox')) {
    const firefoxMatch = userAgent.match(/Firefox\/([0-9.]+)/)
    const version = firefoxMatch ? firefoxMatch[1] : '未知版本'
    return `Mozilla Firefox ${version}`
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    const safariMatch = userAgent.match(/Version\/([0-9.]+)/)
    const version = safariMatch ? safariMatch[1] : '未知版本'
    return `Safari ${version}`
  }

  return userAgent
}

// 获取请求方式的样式类
const getMethodClass = (method) => {
  const methodMap = {
    'GET': 'method-get',
    'POST': 'method-post',
    'PUT': 'method-put',
    'DELETE': 'method-delete',
    'PATCH': 'method-patch'
  }
  return methodMap[method] || 'method-default'
}

// 获取耗时的样式类
const getDurationClass = (duration) => {
  if (duration < 100) return 'duration-fast'
  if (duration < 500) return 'duration-normal'
  if (duration < 1000) return 'duration-slow'
  return 'duration-very-slow'
}
</script>

<style scoped>
.log-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
}

.page-title {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item span {
  min-width: 80px;
  margin-right: 10px;
  font-weight: 500;
  color: #606266;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  margin-top: 10px;
}

.search-icon {
  cursor: pointer;
  color: #409eff;
}

.search-icon:hover {
  color: #66b1ff;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 详情对话框样式 */
.log-detail-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 25px;
  padding: 20px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.section-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.detail-row {
  margin-bottom: 15px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-item {
  display: flex;
  align-items: center;
  min-height: 32px;
  padding: 8px 0;
}

.detail-item.full-width {
  flex-direction: column;
  align-items: flex-start;
}

.detail-label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  margin-right: 12px;
  font-size: 14px;
}

.detail-value {
  color: #303133;
  font-size: 14px;
  word-break: break-all;
  flex: 1;
}

/* IP地址样式 */
.ip-address {
  font-family: 'Courier New', monospace;
  background-color: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
  color: #0066cc;
}

/* 请求方式标签样式 */
.method-tag {
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
}

.method-get {
  background-color: #e7f5e7;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.method-post {
  background-color: #fff2e8;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.method-put {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.method-delete {
  background-color: #fff1f0;
  color: #ff4d4f;
  border: 1px solid #ffb3b3;
}

.method-patch {
  background-color: #f6ffed;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.method-default {
  background-color: #f5f5f5;
  color: #666666;
  border: 1px solid #d9d9d9;
}

/* 耗时样式 */
.duration {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.duration-fast {
  color: #52c41a;
  background-color: #f6ffed;
}

.duration-normal {
  color: #fa8c16;
  background-color: #fff7e6;
}

.duration-slow {
  color: #faad14;
  background-color: #fffbe6;
}

.duration-very-slow {
  color: #ff4d4f;
  background-color: #fff2f0;
}

/* 用户代理样式 */
.user-agent {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-top: 5px;
  font-size: 13px;
  line-height: 1.4;
}

/* 请求参数容器 */
.request-param-container {
  margin-top: 10px;
}

.request-param {
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #3c3c3c;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: center;
  padding: 15px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-container .el-col {
    margin-bottom: 10px;
  }

  .filter-buttons {
    justify-content: center;
  }
}
</style>